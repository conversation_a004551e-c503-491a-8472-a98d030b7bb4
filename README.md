# 🚗 Professional Automotive Diagnostic System

A comprehensive automotive diagnostic system with professional-grade features, enhanced with patterns from leading open-source diagnostic projects including PyRen, UDS-main, DDT4All, and Toyota sample data.

## 🌟 Enhanced Professional Features

### 🔴 **Live Data Monitoring**
- **Real-time Sensor Monitoring**: Engine RPM, temperature, speed, oxygen sensors, etc.
- **Emission Sensors**: O2, NOx, particulate, lambda sensors with real-time analysis
- **Performance Metrics**: Live fuel trim, injection timing, turbo pressure
- **Hybrid/Electric**: Battery SOC, motor torque, inverter temperature
- **Alert System**: Configurable thresholds with real-time notifications
- **Data Logging**: CSV/JSON export with statistical analysis

### 🧪 **Comprehensive Emission Testing**
- **O2 Sensor Testing**: Response time and accuracy testing (PyRen scen_lect_sondeO21 pattern)
- **Lambda Sensor Calibration**: Professional calibration procedures
- **EGR Valve Testing**: Position feedback and operation verification
- **NOx Sensor Diagnostics**: Advanced emission sensor testing
- **Catalyst Efficiency**: Comprehensive catalyst performance analysis
- **Readiness Monitors**: Complete OBD-II readiness status checking

### 🔧 **Professional Unlock Procedures**
- **CLIP Device Emulation**: Renault CLIP diagnostic tool emulation
- **R-Link Unlock**: Hidden function access for Renault R-Link systems
- **Hidden Functions**: Manufacturer-specific diagnostic function unlock
- **Security Bypass**: Professional security access procedures
- **Engineering Mode**: Factory-level diagnostic access

### 🎯 **Macro System & Automation**
- **PyRen-Compatible Macros**: Support for PyRen .cmd macro files
- **Vehicle-Specific Automation**: Model-specific configuration macros
- **CAN/K-Line Initialization**: Automated protocol setup sequences
- **Interactive Procedures**: ANSI terminal-based user interaction
- **Flow Control**: Loops, conditions, and variable substitution

### 🚙 **Model-Specific Configurations**
- **Renault Models**: Megane 2/3, Scenic 2/3, Clio, Captur with specific ECU mappings
- **Toyota Hybrid**: Prius, Camry Hybrid with battery diagnostics
- **VAG Platform**: Golf, Passat, A3, A4 with adaptation channels
- **BMW EDIABAS**: Professional BMW diagnostic patterns
- **Year/Region Variants**: Model year and regional specification support

### 🔌 **Advanced Protocol Support**
- **UDS (ISO 14229)**: Professional UDS implementation with security access
- **CAN Bus**: ISO-TP segmentation and reassembly
- **K-Line/KWP2000**: Legacy protocol support
- **DoIP**: Diagnostics over IP for modern vehicles
- **Brand-Specific**: Manufacturer protocol variations

### 🧠 **AI-Powered Analysis**
- **OpenAI Integration**: GPT-powered diagnostic analysis
- **Cost Estimation**: AI-powered repair cost estimation
- **Maintenance Planning**: Predictive maintenance recommendations
- **Pattern Recognition**: Advanced fault pattern analysis

## 🏗️ Enhanced Architecture

### Reference Project Integration
- **PyRen**: Live parameter monitoring, emission tests, macro system
- **UDS-main**: Professional UDS protocol implementation
- **DDT4All**: ECU communication patterns and Renault-specific procedures
- **Toyota Sample Data**: Real-time data processing and hybrid diagnostics
- **AndrOBD**: Android OBD patterns and user interface concepts
- **EcuBus-Pro**: Modular architecture and component isolation

```
app/
├── obd_interface/          # Enhanced OBD2 and CAN communication
│   ├── obd_reader.py      # ELM327 interface
│   ├── can_reader.py      # CAN/UDS interface
│   ├── dtc_parser.py      # DTC interpretation
│   ├── uds_transport.py   # Professional UDS transport (UDS-main patterns)
│   ├── uds_session.py     # UDS session management
│   ├── ecu_communicator.py # Brand-specific ECU communication
│   ├── brand_protocols.py # Brand protocol factory
│   └── data_processor.py  # Real-time data processing
├── live_data/             # Live data monitoring system
│   ├── live_monitor.py    # Real-time sensor monitoring
│   ├── emission_tester.py # Comprehensive emission testing
│   ├── parameter_monitor.py # Parameter monitoring
│   └── data_logger.py     # Data logging and export
├── macro_system/          # PyRen-inspired macro system
│   ├── macro_engine.py    # Macro execution engine
│   ├── unlock_procedures.py # Professional unlock procedures
│   ├── ansi_terminal.py   # ANSI terminal control
│   ├── model_configs.py   # Model-specific configurations
│   └── vehicle_macros.py  # Vehicle-specific macros
├── ai_engine/             # AI analysis engine
│   ├── prompt_builder.py  # AI prompt generation
│   └── explain_dtc.py     # AI-powered analysis
├── brand_profiles/        # Enhanced brand-specific logic
│   ├── toyota.py          # Toyota/Lexus profiles with hybrid support
│   ├── vag.py            # VW/Audi/Skoda/Seat profiles with adaptations
│   ├── bmw.py            # BMW/MINI profiles with EDIABAS patterns
│   ├── renault.py        # Renault profiles with DDT4All patterns
│   └── nissan.py         # Nissan profiles
├── api/                   # FastAPI web service
│   ├── models.py         # Pydantic models
│   ├── routes.py         # API endpoints
│   ├── live_data_routes.py # Live data API endpoints
│   └── macro_routes.py   # Macro system API endpoints
├── data/                  # Enhanced data management
│   ├── dtc_codes.db      # SQLite DTC database
│   ├── ecu_parameters.json # ECU parameter definitions
│   ├── model_configs/    # Model-specific configurations
│   └── macros/           # PyRen-compatible macro files
├── references/            # Open-source project references
│   ├── pyren-pyren3/     # PyRen project reference
│   ├── uds-main/         # UDS implementation reference
│   ├── ddt4all-master/   # DDT4All reference
│   ├── toyota-sample-obd-data/ # Toyota sample data
│   └── ediabaslib-master/ # BMW EDIABAS reference
└── utils/                 # Utility functions
    └── helpers.py        # Common utilities
```

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- OBD2 adapter (ELM327 compatible)
- Optional: CAN interface for advanced diagnostics

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd machina
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Configure environment**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Run the application**
```bash
python -m uvicorn app.main:app --reload
```

### Docker Deployment

1. **Using Docker Compose**
```bash
docker-compose up -d
```

2. **Access the API**
- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health

## 📡 Enhanced API Usage

### Core Diagnostic Functions

#### Connect to OBD2 Adapter
```bash
curl -X POST "http://localhost:8000/api/v1/connect" \
  -H "Content-Type: application/json" \
  -d '{
    "connection_type": "usb",
    "port": "/dev/ttyUSB0",
    "baudrate": 38400
  }'
```

#### Perform Diagnostic Scan
```bash
curl -X POST "http://localhost:8000/api/v1/scan" \
  -H "Content-Type: application/json" \
  -d '{
    "include_dtcs": true,
    "include_parameters": true,
    "include_freeze_frame": false
  }'
```

### Live Data Monitoring

#### Start Live Monitoring
```bash
curl -X POST "http://localhost:8000/api/v1/live-data/start" \
  -H "Content-Type: application/json" \
  -d '{
    "sensors": ["engine_rpm", "engine_coolant_temp", "vehicle_speed"],
    "update_rate": 10.0,
    "alert_thresholds": {
      "engine_coolant_temp": {"max": 110.0, "critical_high": 120.0}
    }
  }'
```

#### Get Current Readings
```bash
curl -X GET "http://localhost:8000/api/v1/live-data/current?sensors=engine_rpm,vehicle_speed"
```

#### Export Data
```bash
curl -X POST "http://localhost:8000/api/v1/live-data/export" \
  -H "Content-Type: application/json" \
  -d '{
    "sensor": "engine_rpm",
    "format": "csv",
    "duration_hours": 1
  }'
```

### Emission Testing

#### Start O2 Sensor Test
```bash
curl -X POST "http://localhost:8000/api/v1/emission-tests/start" \
  -H "Content-Type: application/json" \
  -d '{
    "test_type": "oxygen_sensor_test",
    "ecu_address": 16
  }'
```

#### Get Test Status
```bash
curl -X GET "http://localhost:8000/api/v1/emission-tests/{test_id}/status"
```

### Unlock Procedures

#### List Available Unlock Procedures
```bash
curl -X GET "http://localhost:8000/api/v1/unlock/procedures"
```

#### Execute R-Link Unlock
```bash
curl -X POST "http://localhost:8000/api/v1/unlock/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "procedure_id": "rlink_unlock",
    "confirm": true,
    "vehicle_model": "renault_megane_3"
  }'
```

### Macro System

#### Execute Macro
```bash
curl -X POST "http://localhost:8000/api/v1/macros/execute" \
  -H "Content-Type: application/json" \
  -d '{
    "macro_name": "can500",
    "variables": {
      "addr": "7A",
      "txa": "7E0"
    }
  }'
```

#### Upload Custom Macro
```bash
curl -X POST "http://localhost:8000/api/v1/macros/upload" \
  -F "file=@custom_macro.cmd" \
  -F "description=Custom vehicle configuration macro"
```

### Model-Specific Functions

#### Get Vehicle Configuration
```bash
curl -X GET "http://localhost:8000/api/v1/models/renault_megane_2/config"
```

#### Execute Special Function
```bash
curl -X POST "http://localhost:8000/api/v1/models/renault_megane_2/functions/tpms_disable" \
  -H "Content-Type: application/json" \
  -d '{
    "ecu_address": 48,
    "confirm": true
  }'
```

### AI Analysis
```bash
curl -X POST "http://localhost:8000/api/v1/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "vehicle_info": {
      "make": "Toyota",
      "model": "Prius",
      "year": 2015
    },
    "include_brand_specific": true,
    "include_cost_estimate": true,
    "live_data": true,
    "emission_test_results": true
  }'
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DEBUG` | Enable debug mode | `false` |
| `OBD_PORT` | OBD2 adapter port | `/dev/ttyUSB0` |
| `OPENAI_API_KEY` | OpenAI API key | None |
| `USE_LOCAL_LLM` | Use local LLM instead of OpenAI | `false` |
| `CAN_INTERFACE` | CAN interface name | `can0` |

### OBD2 Adapter Setup

1. **USB Connection**
   - Connect ELM327 adapter to USB port
   - Verify device appears as `/dev/ttyUSB0` (Linux) or `COM3` (Windows)

2. **Bluetooth Connection**
   - Pair ELM327 adapter with system
   - Use appropriate Bluetooth serial port

3. **CAN Interface**
   - Configure CAN interface: `sudo ip link set can0 type can bitrate 500000`
   - Bring up interface: `sudo ip link set up can0`

## 🧠 AI Integration

### OpenAI Configuration
```bash
export OPENAI_API_KEY="your-api-key-here"
export OPENAI_MODEL="gpt-3.5-turbo"
```

### Local LLM Support
```bash
export USE_LOCAL_LLM=true
export LOCAL_LLM_MODEL_PATH="/path/to/model.bin"
```

## 🚗 Supported Vehicles

### Toyota/Lexus
- **Models**: Prius, Camry, Corolla, RAV4, Highlander, Lexus ES/RX/NX
- **Special Features**: Hybrid system diagnostics, VVT-i analysis
- **Known Issues**: Oil consumption, hybrid battery health, EGR problems

### VAG Group (VW/Audi/Skoda/Seat)
- **Models**: Golf, Jetta, Passat, A3, A4, A6, Octavia
- **Special Features**: DSG diagnostics, DPF regeneration, VCDS integration
- **Known Issues**: Carbon buildup, timing chain, DPF clogging

### BMW/MINI
- **Models**: 3/5/7 Series, X1/X3/X5, MINI Cooper
- **Special Features**: Valvetronic diagnostics, VANOS analysis, ISTA integration
- **Known Issues**: HPFP failure, timing chain stretch, water pump

## 📊 Data Sources

### Open Source References
- **Toyota**: [OBD-PIDs-for-Hybrid-Cars](https://github.com/iwanders/OBD-PIDs-for-Hybrid-Cars)
- **VAG Group**: [vag-obd](https://github.com/M0RtY/vag-obd)
- **BMW**: [ediabaslib](https://github.com/uholeschak/ediabaslib)

### DTC Database
- Comprehensive DTC definitions with causes and solutions
- Brand-specific interpretations and procedures
- Freeze frame data analysis

## 🧪 Testing

### Unit Tests
```bash
pytest tests/ -v
```

### Integration Tests
```bash
pytest tests/integration/ -v
```

### OBD2 Simulator
```bash
# Install OBD2 simulator for testing
pip install obd-simulator
python -m obd_simulator --port /dev/pts/1
```

## 📈 Monitoring

### Metrics
- API response times
- Diagnostic scan success rates
- AI analysis accuracy
- System resource usage

### Logging
- Structured logging with loguru
- Audit trail for all operations
- Error tracking and alerting

## 🔒 Security

### Best Practices
- API key management
- Input validation
- Rate limiting
- Secure communication

### Data Privacy
- No VIN storage without consent
- Encrypted data transmission
- GDPR compliance considerations

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Development Setup
```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run pre-commit hooks
pre-commit install

# Run tests
pytest
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Documentation
- API Documentation: `/docs`
- OpenAPI Spec: `/openapi.json`

### Community
- GitHub Issues for bug reports
- Discussions for feature requests
- Wiki for additional documentation

### Commercial Support
Contact for enterprise support and custom integrations.

## 🗺️ Enhanced Roadmap

### Phase 1 (Completed) ✅
- ✅ Basic OBD2 communication
- ✅ AI-powered analysis
- ✅ Brand-specific profiles
- ✅ RESTful API
- ✅ Live data monitoring system
- ✅ Comprehensive emission testing
- ✅ Professional unlock procedures
- ✅ PyRen-compatible macro system
- ✅ Model-specific configurations
- ✅ UDS protocol implementation

### Phase 2 (Current) 🔄
- 🔄 Web dashboard with live data visualization
- 🔄 Mobile app integration
- 🔄 Advanced CAN diagnostics with J2534 support
- 🔄 Machine learning models for predictive analysis
- 🔄 Enhanced security access algorithms
- 🔄 Professional diagnostic report generation
- 🔄 Multi-language support for international markets

### Phase 3 (Planned) 📋
- 📋 Predictive maintenance with ML models
- 📋 Fleet management capabilities
- 📋 Cloud-based diagnostic data sync
- 📋 IoT connectivity for remote diagnostics
- 📋 Blockchain integration for service records
- 📋 AR/VR integration for guided repairs
- 📋 Professional workshop management system

### Phase 4 (Future Vision) 🚀
- 🚀 Autonomous diagnostic AI
- 🚀 Real-time vehicle health monitoring
- 🚀 Integration with vehicle manufacturers
- 🚀 Global diagnostic knowledge base
- 🚀 Advanced telematics integration
- 🚀 Quantum-resistant security protocols

---

**Built with ❤️ for the automotive community**
