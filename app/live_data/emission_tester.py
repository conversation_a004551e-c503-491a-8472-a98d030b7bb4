"""
Emission Tester
Comprehensive emission testing inspired by PyRen emission test scenarios

Features:
- O2 sensor testing (scen_lect_sondeO21 pattern)
- NOx sensor diagnostics
- Particulate filter testing
- EGR valve testing
- Catalyst efficiency testing
- Lambda sensor calibration

Follows PyRen scenarium patterns with modern async implementation.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import time

from .live_monitor import LiveDataMonitor, SensorType, LiveDataPoint
from ..obd_interface.uds_session import UDSSession

logger = logging.getLogger(__name__)


class EmissionTestType(Enum):
    """Types of emission tests"""
    OXYGEN_SENSOR_TEST = "oxygen_sensor_test"
    LAMBDA_SENSOR_CALIBRATION = "lambda_sensor_calibration"
    NOX_SENSOR_DIAGNOSTIC = "nox_sensor_diagnostic"
    PARTICULATE_FILTER_TEST = "particulate_filter_test"
    EGR_VALVE_TEST = "egr_valve_test"
    CATALYST_EFFICIENCY_TEST = "catalyst_efficiency_test"
    EVAP_SYSTEM_TEST = "evap_system_test"
    SECONDARY_AIR_TEST = "secondary_air_test"
    FUEL_TRIM_TEST = "fuel_trim_test"
    READINESS_MONITOR_TEST = "readiness_monitor_test"


class TestPhase(Enum):
    """Test execution phases"""
    INITIALIZATION = "initialization"
    PRECONDITION_CHECK = "precondition_check"
    TEST_EXECUTION = "test_execution"
    DATA_COLLECTION = "data_collection"
    ANALYSIS = "analysis"
    COMPLETION = "completion"
    ERROR = "error"


class TestResult(Enum):
    """Test result status"""
    PASS = "pass"
    FAIL = "fail"
    INCOMPLETE = "incomplete"
    ERROR = "error"
    ABORTED = "aborted"


@dataclass
class EmissionTestCondition:
    """Test precondition definition"""
    name: str
    description: str
    sensor_type: SensorType
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    target_value: Optional[float] = None
    tolerance: Optional[float] = None
    duration: Optional[timedelta] = None


@dataclass
class EmissionTestStep:
    """Individual test step"""
    step_id: str
    name: str
    description: str
    duration: timedelta
    commands: List[str] = field(default_factory=list)
    expected_responses: List[str] = field(default_factory=list)
    data_collection: List[SensorType] = field(default_factory=list)


@dataclass
class EmissionTest:
    """Emission test definition"""
    test_type: EmissionTestType
    name: str
    description: str
    preconditions: List[EmissionTestCondition] = field(default_factory=list)
    test_steps: List[EmissionTestStep] = field(default_factory=list)
    max_duration: timedelta = timedelta(minutes=30)
    required_sensors: List[SensorType] = field(default_factory=list)
    ecu_address: Optional[int] = None


@dataclass
class EmissionResult:
    """Emission test result"""
    test_type: EmissionTestType
    test_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    result: TestResult = TestResult.INCOMPLETE
    current_phase: TestPhase = TestPhase.INITIALIZATION
    error_message: Optional[str] = None
    
    # Test data
    collected_data: Dict[SensorType, List[LiveDataPoint]] = field(default_factory=dict)
    measurements: Dict[str, Any] = field(default_factory=dict)
    analysis_results: Dict[str, Any] = field(default_factory=dict)
    
    # Progress tracking
    completed_steps: List[str] = field(default_factory=list)
    failed_conditions: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'test_type': self.test_type.value,
            'test_name': self.test_name,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'result': self.result.value,
            'current_phase': self.current_phase.value,
            'error_message': self.error_message,
            'measurements': self.measurements,
            'analysis_results': self.analysis_results,
            'completed_steps': self.completed_steps,
            'failed_conditions': self.failed_conditions
        }


class EmissionTester:
    """
    Emission Tester
    Comprehensive emission testing with PyRen-inspired patterns
    """
    
    def __init__(self, live_monitor: LiveDataMonitor, uds_session: Optional[UDSSession] = None):
        self.live_monitor = live_monitor
        self.uds_session = uds_session
        
        # Test definitions
        self.test_definitions: Dict[EmissionTestType, EmissionTest] = {}
        self.active_tests: Dict[str, EmissionResult] = {}
        
        # Event handlers
        self.progress_handlers: List[Callable[[EmissionResult], None]] = []
        self.completion_handlers: List[Callable[[EmissionResult], None]] = []
        
        self._initialize_standard_tests()
    
    def _initialize_standard_tests(self):
        """Initialize standard emission tests"""
        # Oxygen Sensor Test (inspired by scen_lect_sondeO21)
        o2_test = EmissionTest(
            test_type=EmissionTestType.OXYGEN_SENSOR_TEST,
            name="Oxygen Sensor Response Test",
            description="Test oxygen sensor response time and accuracy",
            preconditions=[
                EmissionTestCondition(
                    name="Engine Temperature",
                    description="Engine must be at operating temperature",
                    sensor_type=SensorType.ENGINE_COOLANT_TEMP,
                    min_value=80.0,
                    max_value=110.0
                ),
                EmissionTestCondition(
                    name="Engine RPM",
                    description="Engine must be at idle",
                    sensor_type=SensorType.ENGINE_RPM,
                    min_value=600.0,
                    max_value=1000.0,
                    duration=timedelta(seconds=30)
                )
            ],
            test_steps=[
                EmissionTestStep(
                    step_id="o2_baseline",
                    name="Baseline Reading",
                    description="Record baseline O2 sensor readings",
                    duration=timedelta(seconds=60),
                    data_collection=[SensorType.OXYGEN_SENSOR_1, SensorType.OXYGEN_SENSOR_2]
                ),
                EmissionTestStep(
                    step_id="o2_rich_mixture",
                    name="Rich Mixture Test",
                    description="Test sensor response to rich mixture",
                    duration=timedelta(seconds=120),
                    commands=["31 01 20 01"],  # Start rich mixture routine
                    data_collection=[SensorType.OXYGEN_SENSOR_1, SensorType.FUEL_TRIM_SHORT]
                ),
                EmissionTestStep(
                    step_id="o2_lean_mixture",
                    name="Lean Mixture Test",
                    description="Test sensor response to lean mixture",
                    duration=timedelta(seconds=120),
                    commands=["31 01 20 02"],  # Start lean mixture routine
                    data_collection=[SensorType.OXYGEN_SENSOR_1, SensorType.FUEL_TRIM_SHORT]
                )
            ],
            required_sensors=[SensorType.OXYGEN_SENSOR_1, SensorType.ENGINE_COOLANT_TEMP, SensorType.ENGINE_RPM]
        )
        self.test_definitions[EmissionTestType.OXYGEN_SENSOR_TEST] = o2_test
        
        # Lambda Sensor Calibration
        lambda_test = EmissionTest(
            test_type=EmissionTestType.LAMBDA_SENSOR_CALIBRATION,
            name="Lambda Sensor Calibration",
            description="Calibrate lambda sensor for accurate readings",
            preconditions=[
                EmissionTestCondition(
                    name="Engine Temperature",
                    description="Engine must be at operating temperature",
                    sensor_type=SensorType.ENGINE_COOLANT_TEMP,
                    min_value=85.0
                ),
                EmissionTestCondition(
                    name="Lambda Sensor Temperature",
                    description="Lambda sensor must be heated",
                    sensor_type=SensorType.LAMBDA_SENSOR,
                    min_value=0.1  # Sensor active voltage
                )
            ],
            test_steps=[
                EmissionTestStep(
                    step_id="lambda_zero_point",
                    name="Zero Point Calibration",
                    description="Calibrate lambda sensor zero point",
                    duration=timedelta(seconds=180),
                    commands=["31 01 21 01"],  # Lambda calibration routine
                    data_collection=[SensorType.LAMBDA_SENSOR]
                )
            ],
            required_sensors=[SensorType.LAMBDA_SENSOR, SensorType.ENGINE_COOLANT_TEMP]
        )
        self.test_definitions[EmissionTestType.LAMBDA_SENSOR_CALIBRATION] = lambda_test
        
        # EGR Valve Test
        egr_test = EmissionTest(
            test_type=EmissionTestType.EGR_VALVE_TEST,
            name="EGR Valve Function Test",
            description="Test EGR valve operation and position feedback",
            preconditions=[
                EmissionTestCondition(
                    name="Engine Temperature",
                    description="Engine must be warm",
                    sensor_type=SensorType.ENGINE_COOLANT_TEMP,
                    min_value=70.0
                ),
                EmissionTestCondition(
                    name="Engine Load",
                    description="Engine load must be stable",
                    sensor_type=SensorType.ENGINE_LOAD,
                    max_value=30.0
                )
            ],
            test_steps=[
                EmissionTestStep(
                    step_id="egr_close",
                    name="EGR Valve Close Test",
                    description="Command EGR valve to close position",
                    duration=timedelta(seconds=30),
                    commands=["2F 30 01 00"],  # Close EGR valve
                    data_collection=[SensorType.EGR_POSITION, SensorType.MANIFOLD_PRESSURE]
                ),
                EmissionTestStep(
                    step_id="egr_open",
                    name="EGR Valve Open Test",
                    description="Command EGR valve to open position",
                    duration=timedelta(seconds=30),
                    commands=["2F 30 01 FF"],  # Open EGR valve
                    data_collection=[SensorType.EGR_POSITION, SensorType.MANIFOLD_PRESSURE]
                )
            ],
            required_sensors=[SensorType.EGR_POSITION, SensorType.ENGINE_COOLANT_TEMP]
        )
        self.test_definitions[EmissionTestType.EGR_VALVE_TEST] = egr_test
    
    def register_test(self, test: EmissionTest):
        """Register custom emission test"""
        self.test_definitions[test.test_type] = test
        logger.info(f"Registered emission test: {test.name}")
    
    def add_progress_handler(self, handler: Callable[[EmissionResult], None]):
        """Add progress update handler"""
        self.progress_handlers.append(handler)
    
    def add_completion_handler(self, handler: Callable[[EmissionResult], None]):
        """Add test completion handler"""
        self.completion_handlers.append(handler)
    
    async def start_test(self, test_type: EmissionTestType, test_id: Optional[str] = None) -> str:
        """Start emission test"""
        if test_type not in self.test_definitions:
            raise ValueError(f"Unknown test type: {test_type}")
        
        test_def = self.test_definitions[test_type]
        test_id = test_id or f"{test_type.value}_{int(time.time())}"
        
        # Create test result
        result = EmissionResult(
            test_type=test_type,
            test_name=test_def.name,
            start_time=datetime.now()
        )
        
        self.active_tests[test_id] = result
        
        # Start test execution
        asyncio.create_task(self._execute_test(test_id, test_def, result))
        
        logger.info(f"Started emission test: {test_def.name} (ID: {test_id})")
        return test_id
    
    async def stop_test(self, test_id: str):
        """Stop running test"""
        if test_id in self.active_tests:
            result = self.active_tests[test_id]
            result.result = TestResult.ABORTED
            result.end_time = datetime.now()
            result.current_phase = TestPhase.COMPLETION
            
            # Notify handlers
            for handler in self.completion_handlers:
                try:
                    handler(result)
                except Exception as e:
                    logger.error(f"Completion handler error: {e}")
            
            logger.info(f"Stopped test: {test_id}")
    
    async def get_test_status(self, test_id: str) -> Optional[EmissionResult]:
        """Get test status"""
        return self.active_tests.get(test_id)
    
    async def _execute_test(self, test_id: str, test_def: EmissionTest, result: EmissionResult):
        """Execute emission test"""
        try:
            # Phase 1: Check preconditions
            result.current_phase = TestPhase.PRECONDITION_CHECK
            self._notify_progress(result)
            
            if not await self._check_preconditions(test_def, result):
                result.result = TestResult.FAIL
                result.error_message = "Preconditions not met"
                result.end_time = datetime.now()
                result.current_phase = TestPhase.ERROR
                self._notify_completion(result)
                return
            
            # Phase 2: Test execution
            result.current_phase = TestPhase.TEST_EXECUTION
            self._notify_progress(result)
            
            for step in test_def.test_steps:
                if not await self._execute_test_step(step, result):
                    result.result = TestResult.FAIL
                    result.error_message = f"Test step failed: {step.name}"
                    result.end_time = datetime.now()
                    result.current_phase = TestPhase.ERROR
                    self._notify_completion(result)
                    return
                
                result.completed_steps.append(step.step_id)
                self._notify_progress(result)
            
            # Phase 3: Analysis
            result.current_phase = TestPhase.ANALYSIS
            self._notify_progress(result)
            
            await self._analyze_test_results(test_def, result)
            
            # Phase 4: Completion
            result.current_phase = TestPhase.COMPLETION
            result.end_time = datetime.now()
            
            if result.result == TestResult.INCOMPLETE:
                result.result = TestResult.PASS
            
            self._notify_completion(result)
            
        except Exception as e:
            logger.error(f"Test execution error: {e}")
            result.result = TestResult.ERROR
            result.error_message = str(e)
            result.end_time = datetime.now()
            result.current_phase = TestPhase.ERROR
            self._notify_completion(result)
    
    async def _check_preconditions(self, test_def: EmissionTest, result: EmissionResult) -> bool:
        """Check test preconditions"""
        for condition in test_def.preconditions:
            try:
                # Get current sensor reading
                readings = await self.live_monitor.get_current_readings([condition.sensor_type])
                
                if condition.sensor_type not in readings:
                    result.failed_conditions.append(f"Sensor not available: {condition.name}")
                    continue
                
                reading = readings[condition.sensor_type]
                value = reading.value
                
                if not isinstance(value, (int, float)):
                    result.failed_conditions.append(f"Invalid sensor value: {condition.name}")
                    continue
                
                # Check value ranges
                if condition.min_value is not None and value < condition.min_value:
                    result.failed_conditions.append(f"{condition.name}: {value} < {condition.min_value}")
                
                if condition.max_value is not None and value > condition.max_value:
                    result.failed_conditions.append(f"{condition.name}: {value} > {condition.max_value}")
                
                # Check target value with tolerance
                if condition.target_value is not None and condition.tolerance is not None:
                    if abs(value - condition.target_value) > condition.tolerance:
                        result.failed_conditions.append(
                            f"{condition.name}: {value} not within {condition.tolerance} of {condition.target_value}"
                        )
                
                # Check duration stability if required
                if condition.duration is not None:
                    # This would require historical data analysis
                    # For now, we'll assume the condition is met
                    pass
                
            except Exception as e:
                logger.error(f"Error checking condition {condition.name}: {e}")
                result.failed_conditions.append(f"Error checking {condition.name}: {str(e)}")
        
        return len(result.failed_conditions) == 0
    
    async def _execute_test_step(self, step: EmissionTestStep, result: EmissionResult) -> bool:
        """Execute individual test step"""
        try:
            logger.info(f"Executing test step: {step.name}")
            
            # Send commands if any
            if step.commands and self.uds_session:
                for command in step.commands:
                    # Convert hex string to bytes
                    cmd_bytes = bytes.fromhex(command.replace(" ", ""))
                    # This would send the command via UDS session
                    # For now, we'll simulate the command execution
                    logger.info(f"Sending command: {command}")
            
            # Collect data during step duration
            start_time = datetime.now()
            end_time = start_time + step.duration
            
            step_data = {}
            for sensor_type in step.data_collection:
                step_data[sensor_type] = []
            
            while datetime.now() < end_time:
                # Collect sensor readings
                readings = await self.live_monitor.get_current_readings(step.data_collection)
                
                for sensor_type, reading in readings.items():
                    if sensor_type not in result.collected_data:
                        result.collected_data[sensor_type] = []
                    
                    result.collected_data[sensor_type].append(reading)
                    step_data[sensor_type].append(reading)
                
                await asyncio.sleep(0.1)  # 10Hz data collection
            
            # Store step-specific measurements
            result.measurements[step.step_id] = self._calculate_step_measurements(step_data)
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing test step {step.name}: {e}")
            return False
    
    def _calculate_step_measurements(self, step_data: Dict[SensorType, List[LiveDataPoint]]) -> Dict[str, Any]:
        """Calculate measurements for test step"""
        measurements = {}
        
        for sensor_type, data_points in step_data.items():
            if not data_points:
                continue
            
            values = [p.value for p in data_points if isinstance(p.value, (int, float))]
            
            if values:
                measurements[f"{sensor_type.value}_min"] = min(values)
                measurements[f"{sensor_type.value}_max"] = max(values)
                measurements[f"{sensor_type.value}_avg"] = sum(values) / len(values)
                measurements[f"{sensor_type.value}_count"] = len(values)
        
        return measurements
    
    async def _analyze_test_results(self, test_def: EmissionTest, result: EmissionResult):
        """Analyze test results and determine pass/fail"""
        try:
            analysis = {}
            
            # Analyze based on test type
            if test_def.test_type == EmissionTestType.OXYGEN_SENSOR_TEST:
                analysis = await self._analyze_oxygen_sensor_test(result)
            elif test_def.test_type == EmissionTestType.EGR_VALVE_TEST:
                analysis = await self._analyze_egr_valve_test(result)
            # Add more test-specific analysis
            
            result.analysis_results = analysis
            
        except Exception as e:
            logger.error(f"Error analyzing test results: {e}")
            result.analysis_results = {"error": str(e)}
    
    async def _analyze_oxygen_sensor_test(self, result: EmissionResult) -> Dict[str, Any]:
        """Analyze oxygen sensor test results"""
        analysis = {}
        
        # Check if we have O2 sensor data
        if SensorType.OXYGEN_SENSOR_1 in result.collected_data:
            o2_data = result.collected_data[SensorType.OXYGEN_SENSOR_1]
            
            if o2_data:
                values = [p.value for p in o2_data if isinstance(p.value, (int, float))]
                
                if values:
                    analysis["o2_sensor_response_range"] = max(values) - min(values)
                    analysis["o2_sensor_avg_voltage"] = sum(values) / len(values)
                    analysis["o2_sensor_readings_count"] = len(values)
                    
                    # Check response range (should be > 0.6V for healthy sensor)
                    if analysis["o2_sensor_response_range"] > 0.6:
                        analysis["o2_sensor_status"] = "PASS"
                    else:
                        analysis["o2_sensor_status"] = "FAIL"
                        result.result = TestResult.FAIL
        
        return analysis
    
    async def _analyze_egr_valve_test(self, result: EmissionResult) -> Dict[str, Any]:
        """Analyze EGR valve test results"""
        analysis = {}
        
        # Check EGR position response
        if SensorType.EGR_POSITION in result.collected_data:
            egr_data = result.collected_data[SensorType.EGR_POSITION]
            
            if egr_data:
                positions = [p.value for p in egr_data if isinstance(p.value, (int, float))]
                
                if positions:
                    analysis["egr_position_range"] = max(positions) - min(positions)
                    analysis["egr_position_avg"] = sum(positions) / len(positions)
                    
                    # Check if valve moved (range should be > 10% for healthy valve)
                    if analysis["egr_position_range"] > 10.0:
                        analysis["egr_valve_status"] = "PASS"
                    else:
                        analysis["egr_valve_status"] = "FAIL"
                        result.result = TestResult.FAIL
        
        return analysis
    
    def _notify_progress(self, result: EmissionResult):
        """Notify progress handlers"""
        for handler in self.progress_handlers:
            try:
                handler(result)
            except Exception as e:
                logger.error(f"Progress handler error: {e}")
    
    def _notify_completion(self, result: EmissionResult):
        """Notify completion handlers"""
        for handler in self.completion_handlers:
            try:
                handler(result)
            except Exception as e:
                logger.error(f"Completion handler error: {e}")
    
    def get_available_tests(self) -> List[EmissionTestType]:
        """Get list of available emission tests"""
        return list(self.test_definitions.keys())
    
    def get_test_definition(self, test_type: EmissionTestType) -> Optional[EmissionTest]:
        """Get test definition"""
        return self.test_definitions.get(test_type)
