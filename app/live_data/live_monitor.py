"""
Live Data Monitor
Real-time sensor monitoring inspired by PyRen live parameter patterns

Monitors:
- Engine parameters (RPM, load, temperature)
- Emission sensors (O2, NOx, particulates)
- Vehicle dynamics (speed, acceleration)
- Environmental sensors (ambient temp, pressure)
- Hybrid/Electric systems (battery, motor)

Follows PyRen parameter monitoring patterns with modern async architecture.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import time
import json

from ..obd_interface.data_processor import DataProcessor, DataStream, OBDDataPoint

logger = logging.getLogger(__name__)


class SensorType(Enum):
    """Sensor types for live monitoring"""
    # Engine sensors
    ENGINE_RPM = "engine_rpm"
    ENGINE_LOAD = "engine_load"
    ENGINE_COOLANT_TEMP = "engine_coolant_temp"
    ENGINE_OIL_TEMP = "engine_oil_temp"
    ENGINE_OIL_PRESSURE = "engine_oil_pressure"
    INTAKE_AIR_TEMP = "intake_air_temp"
    MANIFOLD_PRESSURE = "manifold_pressure"
    THROTTLE_POSITION = "throttle_position"
    
    # Fuel system
    FUEL_PRESSURE = "fuel_pressure"
    FUEL_LEVEL = "fuel_level"
    FUEL_TRIM_SHORT = "fuel_trim_short"
    FUEL_TRIM_LONG = "fuel_trim_long"
    FUEL_INJECTION_TIMING = "fuel_injection_timing"
    
    # Emission sensors
    OXYGEN_SENSOR_1 = "oxygen_sensor_1"
    OXYGEN_SENSOR_2 = "oxygen_sensor_2"
    LAMBDA_SENSOR = "lambda_sensor"
    NOX_SENSOR = "nox_sensor"
    PARTICULATE_SENSOR = "particulate_sensor"
    EGR_POSITION = "egr_position"
    
    # Vehicle dynamics
    VEHICLE_SPEED = "vehicle_speed"
    WHEEL_SPEED_FL = "wheel_speed_fl"
    WHEEL_SPEED_FR = "wheel_speed_fr"
    WHEEL_SPEED_RL = "wheel_speed_rl"
    WHEEL_SPEED_RR = "wheel_speed_rr"
    
    # Transmission
    TRANSMISSION_TEMP = "transmission_temp"
    GEAR_POSITION = "gear_position"
    TORQUE_CONVERTER = "torque_converter"
    
    # Environmental
    AMBIENT_TEMP = "ambient_temp"
    BAROMETRIC_PRESSURE = "barometric_pressure"
    HUMIDITY = "humidity"
    
    # Hybrid/Electric
    BATTERY_VOLTAGE = "battery_voltage"
    BATTERY_CURRENT = "battery_current"
    BATTERY_SOC = "battery_soc"
    BATTERY_TEMP = "battery_temp"
    MOTOR_RPM = "motor_rpm"
    MOTOR_TORQUE = "motor_torque"
    INVERTER_TEMP = "inverter_temp"


@dataclass
class LiveDataPoint:
    """Live data point structure"""
    sensor_type: SensorType
    timestamp: datetime
    value: Union[float, int, str]
    unit: str
    quality: float = 1.0
    raw_data: Optional[bytes] = None
    ecu_address: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'sensor_type': self.sensor_type.value,
            'timestamp': self.timestamp.isoformat(),
            'value': self.value,
            'unit': self.unit,
            'quality': self.quality,
            'raw_data': self.raw_data.hex() if self.raw_data else None,
            'ecu_address': self.ecu_address
        }


@dataclass
class SensorDefinition:
    """Sensor definition for live monitoring"""
    sensor_type: SensorType
    pid: str
    name: str
    unit: str
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    update_rate: float = 1.0  # Hz
    conversion_func: Optional[Callable[[bytes], Union[float, int, str]]] = None
    validation_func: Optional[Callable[[Any], bool]] = None
    ecu_address: Optional[int] = None
    brand_specific: bool = False


class LiveDataMonitor:
    """
    Live Data Monitor
    Real-time sensor monitoring with PyRen-inspired patterns
    """
    
    def __init__(self, data_processor: DataProcessor):
        self.data_processor = data_processor
        self.sensor_definitions: Dict[SensorType, SensorDefinition] = {}
        self.active_sensors: Dict[SensorType, bool] = {}
        self.monitoring_tasks: Dict[SensorType, asyncio.Task] = {}
        self.is_monitoring = False
        
        # Data handlers
        self.data_handlers: List[Callable[[LiveDataPoint], None]] = []
        self.alert_handlers: List[Callable[[SensorType, str], None]] = []
        
        # Alert thresholds
        self.alert_thresholds: Dict[SensorType, Dict[str, float]] = {}
        
        # Performance tracking
        self.monitoring_stats = {
            'total_readings': 0,
            'failed_readings': 0,
            'start_time': None,
            'last_update': datetime.now()
        }
        
        self._initialize_standard_sensors()
    
    def _initialize_standard_sensors(self):
        """Initialize standard OBD-II sensors"""
        # Engine sensors
        self.register_sensor(SensorDefinition(
            sensor_type=SensorType.ENGINE_RPM,
            pid="0x0C",
            name="Engine RPM",
            unit="rpm",
            min_value=0,
            max_value=8000,
            update_rate=10.0,
            conversion_func=lambda data: int.from_bytes(data, 'big') / 4
        ))
        
        self.register_sensor(SensorDefinition(
            sensor_type=SensorType.ENGINE_LOAD,
            pid="0x04",
            name="Engine Load",
            unit="%",
            min_value=0,
            max_value=100,
            update_rate=5.0,
            conversion_func=lambda data: int.from_bytes(data, 'big') * 100 / 255
        ))
        
        self.register_sensor(SensorDefinition(
            sensor_type=SensorType.ENGINE_COOLANT_TEMP,
            pid="0x05",
            name="Engine Coolant Temperature",
            unit="°C",
            min_value=-40,
            max_value=215,
            update_rate=2.0,
            conversion_func=lambda data: int.from_bytes(data, 'big') - 40
        ))
        
        self.register_sensor(SensorDefinition(
            sensor_type=SensorType.VEHICLE_SPEED,
            pid="0x0D",
            name="Vehicle Speed",
            unit="km/h",
            min_value=0,
            max_value=255,
            update_rate=10.0,
            conversion_func=lambda data: int.from_bytes(data, 'big')
        ))
        
        self.register_sensor(SensorDefinition(
            sensor_type=SensorType.THROTTLE_POSITION,
            pid="0x11",
            name="Throttle Position",
            unit="%",
            min_value=0,
            max_value=100,
            update_rate=10.0,
            conversion_func=lambda data: int.from_bytes(data, 'big') * 100 / 255
        ))
        
        # Oxygen sensors
        self.register_sensor(SensorDefinition(
            sensor_type=SensorType.OXYGEN_SENSOR_1,
            pid="0x14",
            name="Oxygen Sensor 1",
            unit="V",
            min_value=0,
            max_value=1.275,
            update_rate=5.0,
            conversion_func=lambda data: int.from_bytes(data[:1], 'big') * 0.005
        ))
        
        # Fuel system
        self.register_sensor(SensorDefinition(
            sensor_type=SensorType.FUEL_TRIM_SHORT,
            pid="0x06",
            name="Short Term Fuel Trim",
            unit="%",
            min_value=-100,
            max_value=99.2,
            update_rate=2.0,
            conversion_func=lambda data: (int.from_bytes(data, 'big') - 128) * 100 / 128
        ))
        
        # Environmental
        self.register_sensor(SensorDefinition(
            sensor_type=SensorType.INTAKE_AIR_TEMP,
            pid="0x0F",
            name="Intake Air Temperature",
            unit="°C",
            min_value=-40,
            max_value=215,
            update_rate=2.0,
            conversion_func=lambda data: int.from_bytes(data, 'big') - 40
        ))
        
        self.register_sensor(SensorDefinition(
            sensor_type=SensorType.MANIFOLD_PRESSURE,
            pid="0x0B",
            name="Manifold Absolute Pressure",
            unit="kPa",
            min_value=0,
            max_value=255,
            update_rate=5.0,
            conversion_func=lambda data: int.from_bytes(data, 'big')
        ))
    
    def register_sensor(self, sensor_def: SensorDefinition):
        """Register sensor definition"""
        self.sensor_definitions[sensor_def.sensor_type] = sensor_def
        self.active_sensors[sensor_def.sensor_type] = False
        
        # Register with data processor
        stream = DataStream(
            pid=sensor_def.pid,
            name=sensor_def.name,
            unit=sensor_def.unit,
            min_value=sensor_def.min_value,
            max_value=sensor_def.max_value,
            update_rate=sensor_def.update_rate,
            validation_func=sensor_def.validation_func,
            conversion_func=sensor_def.conversion_func
        )
        self.data_processor.register_stream(stream)
        
        logger.info(f"Registered sensor: {sensor_def.name} ({sensor_def.sensor_type.value})")
    
    def set_alert_threshold(self, sensor_type: SensorType, threshold_type: str, value: float):
        """Set alert threshold for sensor"""
        if sensor_type not in self.alert_thresholds:
            self.alert_thresholds[sensor_type] = {}
        
        self.alert_thresholds[sensor_type][threshold_type] = value
        logger.info(f"Set {threshold_type} threshold for {sensor_type.value}: {value}")
    
    def add_data_handler(self, handler: Callable[[LiveDataPoint], None]):
        """Add data handler for live data points"""
        self.data_handlers.append(handler)
    
    def add_alert_handler(self, handler: Callable[[SensorType, str], None]):
        """Add alert handler for threshold violations"""
        self.alert_handlers.append(handler)
    
    async def start_monitoring(self, sensors: Optional[List[SensorType]] = None):
        """Start monitoring specified sensors"""
        if self.is_monitoring:
            logger.warning("Monitoring already active")
            return
        
        target_sensors = sensors or list(self.sensor_definitions.keys())
        
        self.is_monitoring = True
        self.monitoring_stats['start_time'] = datetime.now()
        
        # Start monitoring tasks for each sensor
        for sensor_type in target_sensors:
            if sensor_type in self.sensor_definitions:
                self.active_sensors[sensor_type] = True
                task = asyncio.create_task(self._monitor_sensor(sensor_type))
                self.monitoring_tasks[sensor_type] = task
        
        logger.info(f"Started monitoring {len(target_sensors)} sensors")
    
    async def stop_monitoring(self, sensors: Optional[List[SensorType]] = None):
        """Stop monitoring specified sensors"""
        target_sensors = sensors or list(self.monitoring_tasks.keys())
        
        for sensor_type in target_sensors:
            if sensor_type in self.monitoring_tasks:
                self.active_sensors[sensor_type] = False
                task = self.monitoring_tasks[sensor_type]
                task.cancel()
                
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                
                del self.monitoring_tasks[sensor_type]
        
        if not self.monitoring_tasks:
            self.is_monitoring = False
        
        logger.info(f"Stopped monitoring {len(target_sensors)} sensors")
    
    async def _monitor_sensor(self, sensor_type: SensorType):
        """Monitor individual sensor"""
        sensor_def = self.sensor_definitions[sensor_type]
        update_interval = 1.0 / sensor_def.update_rate
        
        while self.active_sensors.get(sensor_type, False):
            try:
                # Get latest data from processor
                latest_data = await self.data_processor.get_latest_data([sensor_def.pid])
                
                if sensor_def.pid in latest_data:
                    obd_point = latest_data[sensor_def.pid]
                    
                    # Convert to live data point
                    live_point = LiveDataPoint(
                        sensor_type=sensor_type,
                        timestamp=obd_point.timestamp,
                        value=obd_point.value,
                        unit=obd_point.unit,
                        quality=obd_point.quality,
                        raw_data=obd_point.raw_value,
                        ecu_address=sensor_def.ecu_address
                    )
                    
                    # Check thresholds
                    await self._check_thresholds(live_point)
                    
                    # Notify handlers
                    for handler in self.data_handlers:
                        try:
                            handler(live_point)
                        except Exception as e:
                            logger.error(f"Data handler error: {e}")
                    
                    self.monitoring_stats['total_readings'] += 1
                else:
                    self.monitoring_stats['failed_readings'] += 1
                
                await asyncio.sleep(update_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error monitoring {sensor_type.value}: {e}")
                self.monitoring_stats['failed_readings'] += 1
                await asyncio.sleep(update_interval)
    
    async def _check_thresholds(self, data_point: LiveDataPoint):
        """Check alert thresholds for data point"""
        sensor_type = data_point.sensor_type
        
        if sensor_type not in self.alert_thresholds:
            return
        
        thresholds = self.alert_thresholds[sensor_type]
        value = data_point.value
        
        if not isinstance(value, (int, float)):
            return
        
        # Check various threshold types
        alerts = []
        
        if 'max' in thresholds and value > thresholds['max']:
            alerts.append(f"Value {value} exceeds maximum threshold {thresholds['max']}")
        
        if 'min' in thresholds and value < thresholds['min']:
            alerts.append(f"Value {value} below minimum threshold {thresholds['min']}")
        
        if 'critical_high' in thresholds and value > thresholds['critical_high']:
            alerts.append(f"CRITICAL: Value {value} exceeds critical high threshold {thresholds['critical_high']}")
        
        if 'critical_low' in thresholds and value < thresholds['critical_low']:
            alerts.append(f"CRITICAL: Value {value} below critical low threshold {thresholds['critical_low']}")
        
        # Notify alert handlers
        for alert_msg in alerts:
            for handler in self.alert_handlers:
                try:
                    handler(sensor_type, alert_msg)
                except Exception as e:
                    logger.error(f"Alert handler error: {e}")
    
    async def get_current_readings(self, sensors: Optional[List[SensorType]] = None) -> Dict[SensorType, LiveDataPoint]:
        """Get current readings for specified sensors"""
        target_sensors = sensors or list(self.sensor_definitions.keys())
        readings = {}
        
        for sensor_type in target_sensors:
            if sensor_type in self.sensor_definitions:
                sensor_def = self.sensor_definitions[sensor_type]
                latest_data = await self.data_processor.get_latest_data([sensor_def.pid])
                
                if sensor_def.pid in latest_data:
                    obd_point = latest_data[sensor_def.pid]
                    readings[sensor_type] = LiveDataPoint(
                        sensor_type=sensor_type,
                        timestamp=obd_point.timestamp,
                        value=obd_point.value,
                        unit=obd_point.unit,
                        quality=obd_point.quality,
                        raw_data=obd_point.raw_value,
                        ecu_address=sensor_def.ecu_address
                    )
        
        return readings
    
    async def get_sensor_statistics(self, sensor_type: SensorType, duration: Optional[timedelta] = None) -> Dict[str, Any]:
        """Get statistics for sensor over specified duration"""
        if sensor_type not in self.sensor_definitions:
            return {}
        
        sensor_def = self.sensor_definitions[sensor_type]
        return await self.data_processor.get_statistics(sensor_def.pid, duration)
    
    def get_monitoring_statistics(self) -> Dict[str, Any]:
        """Get monitoring performance statistics"""
        stats = self.monitoring_stats.copy()
        
        if stats['start_time']:
            runtime = datetime.now() - stats['start_time']
            stats['runtime_seconds'] = runtime.total_seconds()
            
            if stats['total_readings'] > 0:
                stats['success_rate'] = (stats['total_readings'] - stats['failed_readings']) / stats['total_readings']
                stats['readings_per_second'] = stats['total_readings'] / runtime.total_seconds()
        
        stats['active_sensors'] = sum(1 for active in self.active_sensors.values() if active)
        stats['total_sensors'] = len(self.sensor_definitions)
        
        return stats
    
    def export_sensor_data(self, sensor_type: SensorType, filename: str, duration: Optional[timedelta] = None):
        """Export sensor data to file"""
        if sensor_type not in self.sensor_definitions:
            logger.error(f"Unknown sensor type: {sensor_type}")
            return
        
        sensor_def = self.sensor_definitions[sensor_type]
        
        # Use data processor export functionality
        asyncio.create_task(
            self.data_processor.export_to_csv(filename, sensor_def.pid, duration)
        )
