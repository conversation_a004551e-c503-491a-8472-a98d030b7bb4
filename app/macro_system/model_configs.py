"""
Model-Specific Configurations
Vehicle model-specific configurations inspired by PyRen macro organization

Features:
- Model-specific ECU configurations
- Brand-specific parameter mappings
- Vehicle-specific unlock procedures
- Model year variations
- Regional differences

Based on PyRen macro organization from references/pyren-pyren3/pyren3/macro/
"""
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
import json
import os

logger = logging.getLogger(__name__)


class VehicleBrand(Enum):
    """Supported vehicle brands"""
    RENAULT = "renault"
    TOYOTA = "toyota"
    VOLKSWAGEN = "volkswagen"
    AUDI = "audi"
    SKODA = "skoda"
    SEAT = "seat"
    BMW = "bmw"
    MERCEDES = "mercedes"
    PEUGEOT = "peugeot"
    CITROEN = "citroen"
    NISSAN = "nissan"
    HONDA = "honda"
    FORD = "ford"
    OPEL = "opel"


class ECUType(Enum):
    """ECU types across different brands"""
    ENGINE = "engine"
    TRANSMISSION = "transmission"
    ABS_ESP = "abs_esp"
    AIRBAG = "airbag"
    BODY_CONTROL = "body_control"
    INSTRUMENT_CLUSTER = "instrument_cluster"
    RADIO_NAVIGATION = "radio_navigation"
    AIR_CONDITIONING = "air_conditioning"
    PARKING_ASSISTANCE = "parking_assistance"
    
    # Renault specific
    UCH = "uch"  # Unified Control Housing
    UCE = "uce"  # Engine Control Unit
    
    # BMW specific
    DME = "dme"  # Digital Motor Electronics
    DDE = "dde"  # Digital Diesel Electronics
    CAS = "cas"  # Car Access System
    
    # Toyota specific
    ECM = "ecm"  # Engine Control Module
    PCM = "pcm"  # Powertrain Control Module


@dataclass
class ECUConfiguration:
    """ECU configuration for specific model"""
    ecu_type: ECUType
    address: int
    name: str
    supported_services: List[int] = field(default_factory=list)
    security_levels: List[int] = field(default_factory=list)
    diagnostic_sessions: List[int] = field(default_factory=list)
    special_procedures: List[str] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)


@dataclass
class VehicleModel:
    """Vehicle model definition"""
    brand: VehicleBrand
    model_name: str
    generation: str
    year_range: tuple  # (start_year, end_year)
    engine_codes: List[str] = field(default_factory=list)
    body_types: List[str] = field(default_factory=list)
    regions: List[str] = field(default_factory=list)
    
    # Technical specifications
    ecus: Dict[ECUType, ECUConfiguration] = field(default_factory=dict)
    can_speed: int = 500  # kbps
    protocol: str = "CAN"
    
    # Model-specific features
    features: List[str] = field(default_factory=list)
    unlock_procedures: List[str] = field(default_factory=list)
    special_functions: Dict[str, Any] = field(default_factory=dict)


@dataclass
class VehicleConfig:
    """Complete vehicle configuration"""
    model: VehicleModel
    vin: Optional[str] = None
    detected_ecus: List[int] = field(default_factory=list)
    active_features: List[str] = field(default_factory=list)
    custom_parameters: Dict[str, Any] = field(default_factory=dict)


class ModelConfigManager:
    """
    Model Configuration Manager
    Manages vehicle model-specific configurations
    """
    
    def __init__(self, config_directory: Optional[str] = None):
        self.config_directory = config_directory or "configs/models"
        self.models: Dict[str, VehicleModel] = {}
        self.brand_configs: Dict[VehicleBrand, Dict[str, Any]] = {}
        
        self._initialize_standard_models()
    
    def _initialize_standard_models(self):
        """Initialize standard vehicle models"""
        # Renault Megane 2/Scenic 2 (inspired by PyRen Megane2Scenic2 macros)
        megane2 = VehicleModel(
            brand=VehicleBrand.RENAULT,
            model_name="Megane",
            generation="2",
            year_range=(2002, 2009),
            engine_codes=["K4M", "F4R", "K9K", "M9R"],
            body_types=["Hatchback", "Sedan", "Estate", "Coupe", "Convertible"],
            regions=["Europe", "Turkey", "South America"],
            can_speed=250,
            protocol="CAN",
            features=[
                "TPMS", "DRL", "Auto Window", "Light Sensor",
                "Manual Wipers", "Single Door Unlock"
            ],
            unlock_procedures=["tpms_off", "drl_off", "auto_window_closed"],
            special_functions={
                "tpms_disable": {
                    "description": "Disable TPMS system",
                    "ecus": ["UCH", "TDB"],
                    "commands": ["2E F0 01 00"]
                },
                "drl_disable": {
                    "description": "Disable daytime running lights",
                    "ecus": ["UCH"],
                    "commands": ["2E F0 02 00"]
                }
            }
        )
        
        # Add ECU configurations for Megane 2
        megane2.ecus[ECUType.UCE] = ECUConfiguration(
            ecu_type=ECUType.UCE,
            address=0x10,
            name="Engine Control Unit",
            supported_services=[0x10, 0x22, 0x19, 0x14, 0x27, 0x2E],
            security_levels=[1, 2],
            diagnostic_sessions=[1, 3],
            special_procedures=["injector_coding", "idle_adaptation"]
        )
        
        megane2.ecus[ECUType.UCH] = ECUConfiguration(
            ecu_type=ECUType.UCH,
            address=0x30,
            name="Unified Control Housing",
            supported_services=[0x10, 0x22, 0x19, 0x14, 0x2E, 0x31],
            security_levels=[1],
            diagnostic_sessions=[1, 3],
            special_procedures=["window_config", "light_config", "tpms_config"]
        )
        
        self.models["renault_megane_2"] = megane2
        
        # Renault Megane 3/Scenic 3 (inspired by PyRen Megane3Scenic3 macros)
        megane3 = VehicleModel(
            brand=VehicleBrand.RENAULT,
            model_name="Megane",
            generation="3",
            year_range=(2008, 2016),
            engine_codes=["K4M", "F4R", "K9K", "M9R", "H4J", "R9M"],
            body_types=["Hatchback", "Estate", "Coupe"],
            regions=["Europe", "Turkey", "Russia"],
            can_speed=500,
            protocol="CAN",
            features=[
                "TPMS", "Auto Parking", "Radio Line", "Start/Stop",
                "PTC Heater", "Air Quality Sensor", "Temperature Display"
            ],
            unlock_procedures=["tpms_toggle", "autoparking_toggle", "startstop_hide"],
            special_functions={
                "autoparking_enable": {
                    "description": "Enable automatic parking assistance",
                    "ecus": ["BCM"],
                    "commands": ["2E F0 10 01"]
                },
                "startstop_hide": {
                    "description": "Hide start/stop indicator",
                    "ecus": ["TDB"],
                    "commands": ["2E F0 20 00"]
                }
            }
        )
        
        # Add ECU configurations for Megane 3
        megane3.ecus[ECUType.ENGINE] = ECUConfiguration(
            ecu_type=ECUType.ENGINE,
            address=0x18,
            name="Engine Control Module",
            supported_services=[0x10, 0x22, 0x19, 0x14, 0x27, 0x2E, 0x31],
            security_levels=[1, 2, 3],
            diagnostic_sessions=[1, 2, 3],
            special_procedures=["dpf_regeneration", "injector_coding"]
        )
        
        megane3.ecus[ECUType.BODY_CONTROL] = ECUConfiguration(
            ecu_type=ECUType.BODY_CONTROL,
            address=0x21,
            name="Body Control Module",
            supported_services=[0x10, 0x22, 0x19, 0x14, 0x2E, 0x31],
            security_levels=[1, 2],
            diagnostic_sessions=[1, 3],
            special_procedures=["comfort_config", "lighting_config"]
        )
        
        self.models["renault_megane_3"] = megane3
        
        # Toyota Prius (inspired by Toyota sample data)
        prius = VehicleModel(
            brand=VehicleBrand.TOYOTA,
            model_name="Prius",
            generation="3",
            year_range=(2009, 2015),
            engine_codes=["2ZR-FXE"],
            body_types=["Hatchback"],
            regions=["Global"],
            can_speed=500,
            protocol="CAN",
            features=[
                "Hybrid System", "EV Mode", "Regenerative Braking",
                "Energy Monitor", "Eco Mode", "Power Mode"
            ],
            unlock_procedures=["hybrid_diagnostics", "ev_mode_extend"],
            special_functions={
                "hybrid_battery_test": {
                    "description": "Comprehensive hybrid battery test",
                    "ecus": ["HV_ECU"],
                    "commands": ["22 21 01", "22 21 02"]
                },
                "ev_mode_config": {
                    "description": "Configure EV mode parameters",
                    "ecus": ["HV_ECU"],
                    "commands": ["2E 21 10 01"]
                }
            }
        )
        
        # Add ECU configurations for Prius
        prius.ecus[ECUType.ECM] = ECUConfiguration(
            ecu_type=ECUType.ECM,
            address=0x10,
            name="Engine Control Module",
            supported_services=[0x10, 0x22, 0x19, 0x14, 0x2F],
            security_levels=[1],
            diagnostic_sessions=[1, 2],
            special_procedures=["hybrid_learn", "engine_adaptation"]
        )
        
        prius.ecus[ECUType.BODY_CONTROL] = ECUConfiguration(
            ecu_type=ECUType.BODY_CONTROL,
            address=0x50,
            name="Combination Meter",
            supported_services=[0x10, 0x22, 0x19, 0x14],
            security_levels=[1],
            diagnostic_sessions=[1, 2],
            special_procedures=["display_config", "warning_reset"]
        )
        
        self.models["toyota_prius_3"] = prius
        
        # Volkswagen Golf (VAG platform)
        golf = VehicleModel(
            brand=VehicleBrand.VOLKSWAGEN,
            model_name="Golf",
            generation="6",
            year_range=(2008, 2013),
            engine_codes=["CBZA", "CAXA", "CAYC", "CFHC"],
            body_types=["Hatchback", "Estate", "Convertible"],
            regions=["Europe", "North America"],
            can_speed=500,
            protocol="CAN",
            features=[
                "DSG", "Start/Stop", "Hill Hold", "Parking Sensors",
                "Adaptive Lighting", "Rain Sensor"
            ],
            unlock_procedures=["adaptation_reset", "basic_settings"],
            special_functions={
                "throttle_adaptation": {
                    "description": "Reset throttle body adaptation",
                    "ecus": ["Engine"],
                    "commands": ["2E 00 60 00"]
                },
                "dsg_adaptation": {
                    "description": "DSG transmission adaptation",
                    "ecus": ["Transmission"],
                    "commands": ["31 01 02 01"]
                }
            }
        )
        
        # Add ECU configurations for Golf
        golf.ecus[ECUType.ENGINE] = ECUConfiguration(
            ecu_type=ECUType.ENGINE,
            address=0x01,
            name="Engine Control Module",
            supported_services=[0x10, 0x22, 0x19, 0x14, 0x2E, 0x31],
            security_levels=[1, 2],
            diagnostic_sessions=[1, 3],
            special_procedures=["adaptation_channels", "output_tests"]
        )
        
        golf.ecus[ECUType.TRANSMISSION] = ECUConfiguration(
            ecu_type=ECUType.TRANSMISSION,
            address=0x02,
            name="Transmission Control Module",
            supported_services=[0x10, 0x22, 0x19, 0x14, 0x2E, 0x31],
            security_levels=[1, 2],
            diagnostic_sessions=[1, 3],
            special_procedures=["dsg_adaptation", "clutch_learn"]
        )
        
        self.models["volkswagen_golf_6"] = golf
    
    def register_model(self, model_id: str, model: VehicleModel):
        """Register vehicle model"""
        self.models[model_id] = model
        logger.info(f"Registered vehicle model: {model_id}")
    
    def get_model(self, model_id: str) -> Optional[VehicleModel]:
        """Get vehicle model by ID"""
        return self.models.get(model_id)
    
    def find_models(self, brand: Optional[VehicleBrand] = None, 
                   model_name: Optional[str] = None,
                   year: Optional[int] = None) -> List[VehicleModel]:
        """Find models matching criteria"""
        results = []
        
        for model in self.models.values():
            if brand and model.brand != brand:
                continue
            
            if model_name and model_name.lower() not in model.model_name.lower():
                continue
            
            if year and not (model.year_range[0] <= year <= model.year_range[1]):
                continue
            
            results.append(model)
        
        return results
    
    def get_model_by_vin(self, vin: str) -> Optional[VehicleModel]:
        """Get model by VIN (simplified implementation)"""
        # This is a simplified VIN decoder
        # In a real implementation, you would use a comprehensive VIN database
        
        if len(vin) != 17:
            return None
        
        # Extract manufacturer code (positions 1-3)
        wmi = vin[:3]
        
        # Basic manufacturer mapping
        manufacturer_map = {
            'VF1': VehicleBrand.RENAULT,
            'VF2': VehicleBrand.RENAULT,
            'JTD': VehicleBrand.TOYOTA,
            'WVW': VehicleBrand.VOLKSWAGEN,
            'WAU': VehicleBrand.AUDI,
            'WBA': VehicleBrand.BMW,
            'WDD': VehicleBrand.MERCEDES
        }
        
        brand = manufacturer_map.get(wmi)
        if not brand:
            return None
        
        # Find models for this brand
        brand_models = self.find_models(brand=brand)
        
        # Return first match (in real implementation, decode more VIN details)
        return brand_models[0] if brand_models else None
    
    def create_vehicle_config(self, model_id: str, vin: Optional[str] = None) -> Optional[VehicleConfig]:
        """Create vehicle configuration"""
        model = self.get_model(model_id)
        if not model:
            return None
        
        return VehicleConfig(
            model=model,
            vin=vin,
            detected_ecus=[],
            active_features=[],
            custom_parameters={}
        )
    
    def load_model_configs(self, directory: str):
        """Load model configurations from directory"""
        try:
            for filename in os.listdir(directory):
                if filename.endswith('.json'):
                    filepath = os.path.join(directory, filename)
                    with open(filepath, 'r', encoding='utf-8') as f:
                        config_data = json.load(f)
                    
                    model = self._parse_model_config(config_data)
                    if model:
                        model_id = filename.replace('.json', '')
                        self.register_model(model_id, model)
            
            logger.info(f"Loaded model configurations from {directory}")
            
        except Exception as e:
            logger.error(f"Error loading model configs: {e}")
    
    def _parse_model_config(self, config_data: Dict[str, Any]) -> Optional[VehicleModel]:
        """Parse model configuration from JSON data"""
        try:
            # Parse basic model info
            model = VehicleModel(
                brand=VehicleBrand(config_data['brand']),
                model_name=config_data['model_name'],
                generation=config_data['generation'],
                year_range=tuple(config_data['year_range']),
                engine_codes=config_data.get('engine_codes', []),
                body_types=config_data.get('body_types', []),
                regions=config_data.get('regions', []),
                can_speed=config_data.get('can_speed', 500),
                protocol=config_data.get('protocol', 'CAN'),
                features=config_data.get('features', []),
                unlock_procedures=config_data.get('unlock_procedures', []),
                special_functions=config_data.get('special_functions', {})
            )
            
            # Parse ECU configurations
            if 'ecus' in config_data:
                for ecu_data in config_data['ecus']:
                    ecu_config = ECUConfiguration(
                        ecu_type=ECUType(ecu_data['type']),
                        address=ecu_data['address'],
                        name=ecu_data['name'],
                        supported_services=ecu_data.get('supported_services', []),
                        security_levels=ecu_data.get('security_levels', []),
                        diagnostic_sessions=ecu_data.get('diagnostic_sessions', []),
                        special_procedures=ecu_data.get('special_procedures', []),
                        parameters=ecu_data.get('parameters', {})
                    )
                    model.ecus[ecu_config.ecu_type] = ecu_config
            
            return model
            
        except Exception as e:
            logger.error(f"Error parsing model config: {e}")
            return None
    
    def save_model_config(self, model_id: str, filepath: str):
        """Save model configuration to file"""
        model = self.get_model(model_id)
        if not model:
            raise ValueError(f"Model not found: {model_id}")
        
        try:
            config_data = {
                'brand': model.brand.value,
                'model_name': model.model_name,
                'generation': model.generation,
                'year_range': list(model.year_range),
                'engine_codes': model.engine_codes,
                'body_types': model.body_types,
                'regions': model.regions,
                'can_speed': model.can_speed,
                'protocol': model.protocol,
                'features': model.features,
                'unlock_procedures': model.unlock_procedures,
                'special_functions': model.special_functions,
                'ecus': []
            }
            
            # Add ECU configurations
            for ecu_config in model.ecus.values():
                ecu_data = {
                    'type': ecu_config.ecu_type.value,
                    'address': ecu_config.address,
                    'name': ecu_config.name,
                    'supported_services': ecu_config.supported_services,
                    'security_levels': ecu_config.security_levels,
                    'diagnostic_sessions': ecu_config.diagnostic_sessions,
                    'special_procedures': ecu_config.special_procedures,
                    'parameters': ecu_config.parameters
                }
                config_data['ecus'].append(ecu_data)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved model configuration: {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving model config: {e}")
            raise
    
    def list_models(self) -> List[str]:
        """List all available model IDs"""
        return list(self.models.keys())
    
    def get_model_info(self, model_id: str) -> Optional[Dict[str, Any]]:
        """Get model information"""
        model = self.get_model(model_id)
        if not model:
            return None
        
        return {
            'brand': model.brand.value,
            'model_name': model.model_name,
            'generation': model.generation,
            'year_range': model.year_range,
            'engine_codes': model.engine_codes,
            'body_types': model.body_types,
            'regions': model.regions,
            'can_speed': model.can_speed,
            'protocol': model.protocol,
            'features': model.features,
            'ecu_count': len(model.ecus),
            'unlock_procedures': model.unlock_procedures,
            'special_functions': list(model.special_functions.keys())
        }
    
    def get_ecu_config(self, model_id: str, ecu_type: ECUType) -> Optional[ECUConfiguration]:
        """Get ECU configuration for model"""
        model = self.get_model(model_id)
        if not model:
            return None
        
        return model.ecus.get(ecu_type)
    
    def get_special_functions(self, model_id: str) -> Dict[str, Any]:
        """Get special functions for model"""
        model = self.get_model(model_id)
        if not model:
            return {}
        
        return model.special_functions
