"""
ANSI Terminal Control
Terminal control and formatting inspired by PyRen colorama usage

Features:
- ANSI color codes and formatting
- Screen clearing and cursor control
- Interactive prompts with styling
- Progress indicators
- Status displays

Based on PyRen terminal control patterns and colorama library usage.
"""
import sys
import os
import time
from typing import Optional, Dict, Any
from enum import Enum
from dataclasses import dataclass


class TerminalColor(Enum):
    """ANSI color codes"""
    BLACK = '\033[30m'
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'
    
    # Bright colors
    BRIGHT_BLACK = '\033[90m'
    BRIGHT_RED = '\033[91m'
    BRIGHT_GREEN = '\033[92m'
    BRIGHT_YELLOW = '\033[93m'
    BRIGHT_BLUE = '\033[94m'
    BRIGHT_MAGENTA = '\033[95m'
    BRIGHT_CYAN = '\033[96m'
    BRIGHT_WHITE = '\033[97m'
    
    # Background colors
    BG_BLACK = '\033[40m'
    BG_RED = '\033[41m'
    BG_GREEN = '\033[42m'
    BG_YELLOW = '\033[43m'
    BG_BLUE = '\033[44m'
    BG_MAGENTA = '\033[45m'
    BG_CYAN = '\033[46m'
    BG_WHITE = '\033[47m'


class TerminalStyle(Enum):
    """ANSI style codes"""
    RESET = '\033[0m'
    BOLD = '\033[1m'
    DIM = '\033[2m'
    ITALIC = '\033[3m'
    UNDERLINE = '\033[4m'
    BLINK = '\033[5m'
    REVERSE = '\033[7m'
    STRIKETHROUGH = '\033[9m'


class CursorControl(Enum):
    """ANSI cursor control codes"""
    HIDE = '\033[?25l'
    SHOW = '\033[?25h'
    SAVE_POSITION = '\033[s'
    RESTORE_POSITION = '\033[u'
    HOME = '\033[H'


@dataclass
class TerminalSize:
    """Terminal size information"""
    width: int
    height: int


class ANSITerminal:
    """
    ANSI Terminal Controller
    Provides terminal control and formatting capabilities
    """
    
    def __init__(self, enable_colors: bool = True):
        self.enable_colors = enable_colors and self._supports_color()
        self.current_color = TerminalColor.WHITE
        self.current_style = TerminalStyle.RESET
        
        # Initialize terminal
        if self.enable_colors:
            self._initialize_terminal()
    
    def _supports_color(self) -> bool:
        """Check if terminal supports ANSI colors"""
        # Check common environment variables
        if os.getenv('NO_COLOR'):
            return False
        
        if os.getenv('FORCE_COLOR'):
            return True
        
        # Check if stdout is a TTY
        if not hasattr(sys.stdout, 'isatty') or not sys.stdout.isatty():
            return False
        
        # Check TERM environment variable
        term = os.getenv('TERM', '').lower()
        if 'color' in term or 'ansi' in term or term in ['xterm', 'xterm-256color', 'screen']:
            return True
        
        # Check for Windows
        if os.name == 'nt':
            try:
                import colorama
                colorama.init()
                return True
            except ImportError:
                return False
        
        return True
    
    def _initialize_terminal(self):
        """Initialize terminal for ANSI support"""
        if os.name == 'nt':
            try:
                import colorama
                colorama.init(autoreset=True)
            except ImportError:
                pass
    
    def clear_screen(self):
        """Clear the terminal screen"""
        if self.enable_colors:
            print('\033[2J\033[H', end='')
        else:
            os.system('cls' if os.name == 'nt' else 'clear')
    
    def clear_line(self):
        """Clear current line"""
        if self.enable_colors:
            print('\033[2K\r', end='')
    
    def move_cursor(self, row: int, col: int):
        """Move cursor to specific position"""
        if self.enable_colors:
            print(f'\033[{row};{col}H', end='')
    
    def move_cursor_up(self, lines: int = 1):
        """Move cursor up"""
        if self.enable_colors:
            print(f'\033[{lines}A', end='')
    
    def move_cursor_down(self, lines: int = 1):
        """Move cursor down"""
        if self.enable_colors:
            print(f'\033[{lines}B', end='')
    
    def hide_cursor(self):
        """Hide cursor"""
        if self.enable_colors:
            print(CursorControl.HIDE.value, end='')
    
    def show_cursor(self):
        """Show cursor"""
        if self.enable_colors:
            print(CursorControl.SHOW.value, end='')
    
    def save_cursor_position(self):
        """Save cursor position"""
        if self.enable_colors:
            print(CursorControl.SAVE_POSITION.value, end='')
    
    def restore_cursor_position(self):
        """Restore cursor position"""
        if self.enable_colors:
            print(CursorControl.RESTORE_POSITION.value, end='')
    
    def get_terminal_size(self) -> TerminalSize:
        """Get terminal size"""
        try:
            size = os.get_terminal_size()
            return TerminalSize(size.columns, size.lines)
        except OSError:
            return TerminalSize(80, 24)  # Default size
    
    def colorize(self, text: str, color: TerminalColor, style: Optional[TerminalStyle] = None) -> str:
        """Colorize text with ANSI codes"""
        if not self.enable_colors:
            return text
        
        result = color.value + text
        
        if style:
            result = style.value + result
        
        result += TerminalStyle.RESET.value
        return result
    
    def print_colored(self, text: str, color: TerminalColor, style: Optional[TerminalStyle] = None, end: str = '\n'):
        """Print colored text"""
        colored_text = self.colorize(text, color, style)
        print(colored_text, end=end)
    
    def print_header(self, text: str, width: Optional[int] = None):
        """Print formatted header"""
        terminal_size = self.get_terminal_size()
        width = width or terminal_size.width
        
        # Create header with borders
        border = '=' * width
        padding = (width - len(text) - 2) // 2
        header_line = f"{'=' * padding} {text} {'=' * (width - padding - len(text) - 2)}"
        
        self.print_colored(border, TerminalColor.CYAN, TerminalStyle.BOLD)
        self.print_colored(header_line, TerminalColor.CYAN, TerminalStyle.BOLD)
        self.print_colored(border, TerminalColor.CYAN, TerminalStyle.BOLD)
    
    def print_status(self, status: str, message: str):
        """Print status message with color coding"""
        status_upper = status.upper()
        
        if status_upper in ['OK', 'PASS', 'SUCCESS', 'CONNECTED']:
            color = TerminalColor.GREEN
        elif status_upper in ['ERROR', 'FAIL', 'FAILED', 'CRITICAL']:
            color = TerminalColor.RED
        elif status_upper in ['WARNING', 'WARN', 'CAUTION']:
            color = TerminalColor.YELLOW
        elif status_upper in ['INFO', 'INFORMATION']:
            color = TerminalColor.BLUE
        else:
            color = TerminalColor.WHITE
        
        status_text = f"[{status_upper:>8}]"
        self.print_colored(status_text, color, TerminalStyle.BOLD, end=' ')
        print(message)
    
    def print_progress_bar(self, current: int, total: int, width: int = 50, prefix: str = "Progress"):
        """Print progress bar"""
        if total == 0:
            percentage = 100
        else:
            percentage = int((current / total) * 100)
        
        filled_width = int((current / total) * width) if total > 0 else 0
        bar = '█' * filled_width + '░' * (width - filled_width)
        
        progress_text = f"{prefix}: |{bar}| {percentage:3d}% ({current}/{total})"
        
        # Clear line and print progress
        self.clear_line()
        if percentage == 100:
            self.print_colored(progress_text, TerminalColor.GREEN, end='\r')
        elif percentage >= 75:
            self.print_colored(progress_text, TerminalColor.CYAN, end='\r')
        elif percentage >= 50:
            self.print_colored(progress_text, TerminalColor.YELLOW, end='\r')
        else:
            self.print_colored(progress_text, TerminalColor.WHITE, end='\r')
    
    def print_table(self, headers: list, rows: list, column_widths: Optional[list] = None):
        """Print formatted table"""
        if not rows:
            return
        
        # Calculate column widths if not provided
        if column_widths is None:
            column_widths = []
            for i, header in enumerate(headers):
                max_width = len(header)
                for row in rows:
                    if i < len(row):
                        max_width = max(max_width, len(str(row[i])))
                column_widths.append(max_width + 2)
        
        # Print header
        header_line = ""
        separator_line = ""
        
        for i, (header, width) in enumerate(zip(headers, column_widths)):
            header_line += f"{header:<{width}}"
            separator_line += "─" * width
            
            if i < len(headers) - 1:
                header_line += "│"
                separator_line += "┼"
        
        self.print_colored(header_line, TerminalColor.CYAN, TerminalStyle.BOLD)
        self.print_colored(separator_line, TerminalColor.CYAN)
        
        # Print rows
        for row in rows:
            row_line = ""
            for i, (cell, width) in enumerate(zip(row, column_widths)):
                row_line += f"{str(cell):<{width}}"
                if i < len(row) - 1:
                    row_line += "│"
            
            print(row_line)
    
    def prompt_user(self, message: str, color: TerminalColor = TerminalColor.YELLOW) -> str:
        """Prompt user for input with colored message"""
        colored_prompt = self.colorize(f"{message}: ", color, TerminalStyle.BOLD)
        return input(colored_prompt)
    
    def prompt_yes_no(self, message: str, default: bool = True) -> bool:
        """Prompt user for yes/no input"""
        default_text = "Y/n" if default else "y/N"
        response = self.prompt_user(f"{message} ({default_text})")
        
        if not response:
            return default
        
        return response.lower().startswith('y')
    
    def prompt_choice(self, message: str, choices: list, default: Optional[int] = None) -> int:
        """Prompt user to choose from a list"""
        self.print_colored(message, TerminalColor.YELLOW, TerminalStyle.BOLD)
        
        for i, choice in enumerate(choices, 1):
            marker = ">" if default == i else " "
            self.print_colored(f"{marker} {i}. {choice}", TerminalColor.WHITE)
        
        while True:
            try:
                response = self.prompt_user("Enter choice")
                
                if not response and default is not None:
                    return default
                
                choice_num = int(response)
                if 1 <= choice_num <= len(choices):
                    return choice_num
                else:
                    self.print_status("ERROR", f"Please enter a number between 1 and {len(choices)}")
            
            except ValueError:
                self.print_status("ERROR", "Please enter a valid number")
    
    def display_spinner(self, message: str, duration: float = 1.0):
        """Display spinning progress indicator"""
        spinner_chars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
        
        start_time = time.time()
        i = 0
        
        self.hide_cursor()
        
        try:
            while time.time() - start_time < duration:
                spinner_text = f"{spinner_chars[i % len(spinner_chars)]} {message}"
                self.clear_line()
                self.print_colored(spinner_text, TerminalColor.CYAN, end='\r')
                
                time.sleep(0.1)
                i += 1
            
            # Clear spinner and show completion
            self.clear_line()
            self.print_status("OK", message)
            
        finally:
            self.show_cursor()
    
    def create_box(self, content: list, title: Optional[str] = None, width: Optional[int] = None) -> str:
        """Create a text box with borders"""
        terminal_size = self.get_terminal_size()
        box_width = width or min(terminal_size.width - 4, 80)
        
        # Calculate content width
        content_width = box_width - 4  # Account for borders and padding
        
        # Prepare content lines
        wrapped_lines = []
        for line in content:
            if len(line) <= content_width:
                wrapped_lines.append(line)
            else:
                # Simple word wrapping
                words = line.split()
                current_line = ""
                
                for word in words:
                    if len(current_line + " " + word) <= content_width:
                        current_line += (" " if current_line else "") + word
                    else:
                        if current_line:
                            wrapped_lines.append(current_line)
                        current_line = word
                
                if current_line:
                    wrapped_lines.append(current_line)
        
        # Build box
        box_lines = []
        
        # Top border
        if title:
            title_padding = (box_width - len(title) - 4) // 2
            top_line = f"┌{'─' * title_padding}[ {title} ]{'─' * (box_width - title_padding - len(title) - 4)}┐"
        else:
            top_line = f"┌{'─' * (box_width - 2)}┐"
        
        box_lines.append(top_line)
        
        # Content lines
        for line in wrapped_lines:
            padded_line = f"│ {line:<{content_width}} │"
            box_lines.append(padded_line)
        
        # Bottom border
        bottom_line = f"└{'─' * (box_width - 2)}┘"
        box_lines.append(bottom_line)
        
        return '\n'.join(box_lines)
    
    def print_box(self, content: list, title: Optional[str] = None, width: Optional[int] = None, color: TerminalColor = TerminalColor.CYAN):
        """Print a text box with borders"""
        box_text = self.create_box(content, title, width)
        self.print_colored(box_text, color)
    
    def reset_formatting(self):
        """Reset all formatting to default"""
        if self.enable_colors:
            print(TerminalStyle.RESET.value, end='')
        
        self.current_color = TerminalColor.WHITE
        self.current_style = TerminalStyle.RESET
