"""
Unlock Procedures
Professional diagnostic tool unlock procedures inspired by PyRen unlock macros

Features:
- CLIP device emulation and unlock procedures
- Renault R-Link unlock sequences
- Brand-specific hidden function access
- Security bypass procedures
- Professional diagnostic mode activation

Based on PyRen unlock patterns from references/pyren-pyren3/pyren3/macro/
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import time

from .macro_engine import MacroEngine
from .ansi_terminal import ANSITerminal, TerminalColor
from ..obd_interface.uds_session import UDSSession, DiagnosticSession, SecurityLevel

logger = logging.getLogger(__name__)


class UnlockType(Enum):
    """Types of unlock procedures"""
    CLIP_EMULATION = "clip_emulation"
    RLINK_UNLOCK = "rlink_unlock"
    HIDDEN_FUNCTIONS = "hidden_functions"
    ENGINEERING_MODE = "engineering_mode"
    FACTORY_MODE = "factory_mode"
    DIAGNOSTIC_UNLOCK = "diagnostic_unlock"
    SECURITY_BYPASS = "security_bypass"
    CODING_UNLOCK = "coding_unlock"


class UnlockStatus(Enum):
    """Unlock procedure status"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    WAITING_USER = "waiting_user"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"
    ABORTED = "aborted"


@dataclass
class UnlockStep:
    """Individual unlock step"""
    step_id: str
    name: str
    description: str
    commands: List[str] = field(default_factory=list)
    expected_responses: List[str] = field(default_factory=list)
    timeout: timedelta = timedelta(seconds=30)
    user_interaction: bool = False
    critical: bool = True  # If step fails, abort procedure


@dataclass
class UnlockProcedure:
    """Unlock procedure definition"""
    unlock_type: UnlockType
    name: str
    description: str
    brand: str
    models: List[str] = field(default_factory=list)
    ecu_addresses: List[int] = field(default_factory=list)
    prerequisites: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    steps: List[UnlockStep] = field(default_factory=list)
    max_duration: timedelta = timedelta(minutes=10)
    requires_confirmation: bool = True


@dataclass
class UnlockResult:
    """Unlock procedure result"""
    procedure_name: str
    unlock_type: UnlockType
    start_time: datetime
    end_time: Optional[datetime] = None
    status: UnlockStatus = UnlockStatus.NOT_STARTED
    current_step: Optional[str] = None
    completed_steps: List[str] = field(default_factory=list)
    failed_steps: List[str] = field(default_factory=list)
    error_message: Optional[str] = None
    unlock_data: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'procedure_name': self.procedure_name,
            'unlock_type': self.unlock_type.value,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status.value,
            'current_step': self.current_step,
            'completed_steps': self.completed_steps,
            'failed_steps': self.failed_steps,
            'error_message': self.error_message,
            'unlock_data': self.unlock_data
        }


class UnlockManager:
    """
    Unlock Manager
    Manages professional diagnostic unlock procedures
    """
    
    def __init__(self, uds_session: UDSSession, macro_engine: MacroEngine, terminal: Optional[ANSITerminal] = None):
        self.uds_session = uds_session
        self.macro_engine = macro_engine
        self.terminal = terminal or ANSITerminal()
        
        # Unlock procedures
        self.procedures: Dict[str, UnlockProcedure] = {}
        self.active_unlocks: Dict[str, UnlockResult] = {}
        
        # Event handlers
        self.progress_handlers: List[Callable[[UnlockResult], None]] = []
        self.completion_handlers: List[Callable[[UnlockResult], None]] = []
        
        self._initialize_standard_procedures()
    
    def _initialize_standard_procedures(self):
        """Initialize standard unlock procedures"""
        # Renault R-Link Unlock (inspired by rlink2/unlock.cmd)
        rlink_unlock = UnlockProcedure(
            unlock_type=UnlockType.RLINK_UNLOCK,
            name="Renault R-Link Unlock",
            description="Unlock hidden functions in Renault R-Link system",
            brand="Renault",
            models=["Megane", "Scenic", "Clio", "Captur", "Kadjar"],
            ecu_addresses=[0x13],  # R-Link ECU address
            prerequisites=[
                "Vehicle ignition ON",
                "R-Link system powered",
                "No active communication errors"
            ],
            warnings=[
                "This procedure may void warranty",
                "Ensure stable power supply during unlock",
                "Do not interrupt the procedure"
            ],
            steps=[
                UnlockStep(
                    step_id="rlink_init",
                    name="Initialize R-Link Communication",
                    description="Establish communication with R-Link ECU",
                    commands=["can500", "1003"]
                ),
                UnlockStep(
                    step_id="rlink_security",
                    name="Security Access",
                    description="Request security access to R-Link",
                    commands=["value 1000 7 4 FFFFFFFF", "1101"]
                ),
                UnlockStep(
                    step_id="rlink_unlock_sequence",
                    name="Unlock Sequence",
                    description="Execute unlock sequence",
                    commands=["at sh 69F", "at cra 69F", "at r0"]
                ),
                UnlockStep(
                    step_id="rlink_verify",
                    name="Verify Unlock",
                    description="Verify unlock was successful",
                    commands=["_$hexValue"],
                    user_interaction=True
                )
            ]
        )
        self.procedures["rlink_unlock"] = rlink_unlock
        
        # CLIP Emulation Procedure
        clip_emulation = UnlockProcedure(
            unlock_type=UnlockType.CLIP_EMULATION,
            name="CLIP Device Emulation",
            description="Emulate Renault CLIP diagnostic tool for full access",
            brand="Renault",
            models=["All Renault Models"],
            ecu_addresses=[0x10, 0x30, 0x20, 0x15],
            prerequisites=[
                "Vehicle in diagnostic mode",
                "All ECUs responsive",
                "Stable CAN communication"
            ],
            warnings=[
                "Professional diagnostic tool emulation",
                "May trigger security alerts",
                "Use only for legitimate diagnostic purposes"
            ],
            steps=[
                UnlockStep(
                    step_id="clip_init",
                    name="Initialize CLIP Protocol",
                    description="Initialize CLIP communication protocol",
                    commands=["can500", "at sh 7E0", "at cra 7E8"]
                ),
                UnlockStep(
                    step_id="clip_auth",
                    name="CLIP Authentication",
                    description="Authenticate as CLIP device",
                    commands=["10 03", "27 01"]  # Session control + security access
                ),
                UnlockStep(
                    step_id="clip_unlock",
                    name="Enable CLIP Functions",
                    description="Enable full CLIP diagnostic functions",
                    commands=["31 01 FF 00"]  # Enable all routines
                ),
                UnlockStep(
                    step_id="clip_verify",
                    name="Verify CLIP Access",
                    description="Verify full diagnostic access",
                    commands=["22 F1 87"]  # Read part number
                )
            ]
        )
        self.procedures["clip_emulation"] = clip_emulation
        
        # Hidden Functions Unlock
        hidden_functions = UnlockProcedure(
            unlock_type=UnlockType.HIDDEN_FUNCTIONS,
            name="Hidden Functions Unlock",
            description="Unlock hidden diagnostic and configuration functions",
            brand="Generic",
            models=["Various"],
            ecu_addresses=[0x10],
            prerequisites=[
                "Extended diagnostic session active",
                "Security access level 2 or higher"
            ],
            warnings=[
                "Hidden functions may affect vehicle operation",
                "Some functions are manufacturer-specific",
                "Document all changes made"
            ],
            steps=[
                UnlockStep(
                    step_id="hidden_session",
                    name="Extended Session",
                    description="Enter extended diagnostic session",
                    commands=["10 03"]
                ),
                UnlockStep(
                    step_id="hidden_security",
                    name="Security Access",
                    description="Gain security access",
                    commands=["27 03", "27 04"]  # Request seed, send key
                ),
                UnlockStep(
                    step_id="hidden_enable",
                    name="Enable Hidden Functions",
                    description="Enable hidden diagnostic functions",
                    commands=["2E F0 F0 01"]  # Enable hidden functions DID
                ),
                UnlockStep(
                    step_id="hidden_verify",
                    name="Verify Access",
                    description="Verify hidden functions are accessible",
                    commands=["22 F0 F0"]  # Read hidden functions status
                )
            ]
        )
        self.procedures["hidden_functions"] = hidden_functions
    
    def register_procedure(self, procedure: UnlockProcedure):
        """Register unlock procedure"""
        procedure_id = f"{procedure.brand.lower()}_{procedure.unlock_type.value}"
        self.procedures[procedure_id] = procedure
        logger.info(f"Registered unlock procedure: {procedure.name}")
    
    def add_progress_handler(self, handler: Callable[[UnlockResult], None]):
        """Add progress handler"""
        self.progress_handlers.append(handler)
    
    def add_completion_handler(self, handler: Callable[[UnlockResult], None]):
        """Add completion handler"""
        self.completion_handlers.append(handler)
    
    async def start_unlock(self, procedure_id: str, confirm: bool = True) -> str:
        """Start unlock procedure"""
        if procedure_id not in self.procedures:
            raise ValueError(f"Unknown unlock procedure: {procedure_id}")
        
        procedure = self.procedures[procedure_id]
        
        # Show warnings and get confirmation
        if confirm and procedure.requires_confirmation:
            if not await self._get_user_confirmation(procedure):
                raise ValueError("User cancelled unlock procedure")
        
        # Create unlock result
        unlock_id = f"{procedure_id}_{int(time.time())}"
        result = UnlockResult(
            procedure_name=procedure.name,
            unlock_type=procedure.unlock_type,
            start_time=datetime.now(),
            status=UnlockStatus.IN_PROGRESS
        )
        
        self.active_unlocks[unlock_id] = result
        
        # Start unlock execution
        asyncio.create_task(self._execute_unlock(unlock_id, procedure, result))
        
        logger.info(f"Started unlock procedure: {procedure.name} (ID: {unlock_id})")
        return unlock_id
    
    async def _get_user_confirmation(self, procedure: UnlockProcedure) -> bool:
        """Get user confirmation for unlock procedure"""
        self.terminal.clear_screen()
        self.terminal.print_header(f"UNLOCK PROCEDURE: {procedure.name}")
        
        # Show procedure information
        info_content = [
            f"Type: {procedure.unlock_type.value}",
            f"Brand: {procedure.brand}",
            f"Models: {', '.join(procedure.models)}",
            f"Description: {procedure.description}"
        ]
        self.terminal.print_box(info_content, "Procedure Information", color=TerminalColor.CYAN)
        
        # Show prerequisites
        if procedure.prerequisites:
            self.terminal.print_box(procedure.prerequisites, "Prerequisites", color=TerminalColor.YELLOW)
        
        # Show warnings
        if procedure.warnings:
            self.terminal.print_box(procedure.warnings, "⚠️  WARNINGS", color=TerminalColor.RED)
        
        # Get confirmation
        print()
        self.terminal.print_colored("This unlock procedure may:", TerminalColor.YELLOW, end='\n')
        self.terminal.print_colored("• Modify vehicle configuration", TerminalColor.YELLOW)
        self.terminal.print_colored("• Void warranty if misused", TerminalColor.YELLOW)
        self.terminal.print_colored("• Require professional knowledge", TerminalColor.YELLOW)
        print()
        
        return self.terminal.prompt_yes_no("Do you want to proceed with this unlock procedure?", default=False)
    
    async def _execute_unlock(self, unlock_id: str, procedure: UnlockProcedure, result: UnlockResult):
        """Execute unlock procedure"""
        try:
            self.terminal.clear_screen()
            self.terminal.print_header(f"EXECUTING: {procedure.name}")
            
            # Execute each step
            for i, step in enumerate(procedure.steps):
                result.current_step = step.step_id
                self._notify_progress(result)
                
                self.terminal.print_status("INFO", f"Step {i+1}/{len(procedure.steps)}: {step.name}")
                self.terminal.print_colored(f"  {step.description}", TerminalColor.WHITE)
                
                # Execute step
                step_success = await self._execute_unlock_step(step, result)
                
                if step_success:
                    result.completed_steps.append(step.step_id)
                    self.terminal.print_status("OK", f"Step completed: {step.name}")
                else:
                    result.failed_steps.append(step.step_id)
                    self.terminal.print_status("FAIL", f"Step failed: {step.name}")
                    
                    if step.critical:
                        result.status = UnlockStatus.FAILED
                        result.error_message = f"Critical step failed: {step.name}"
                        result.end_time = datetime.now()
                        self._notify_completion(result)
                        return
                
                # Small delay between steps
                await asyncio.sleep(0.5)
            
            # All steps completed successfully
            result.status = UnlockStatus.COMPLETED
            result.end_time = datetime.now()
            
            self.terminal.print_status("SUCCESS", "Unlock procedure completed successfully!")
            self._notify_completion(result)
            
        except Exception as e:
            logger.error(f"Error executing unlock procedure: {e}")
            result.status = UnlockStatus.FAILED
            result.error_message = str(e)
            result.end_time = datetime.now()
            self._notify_completion(result)
    
    async def _execute_unlock_step(self, step: UnlockStep, result: UnlockResult) -> bool:
        """Execute individual unlock step"""
        try:
            # Handle user interaction steps
            if step.user_interaction:
                return await self._handle_user_interaction_step(step, result)
            
            # Execute commands
            for command in step.commands:
                success = await self._execute_unlock_command(command, step, result)
                if not success:
                    return False
                
                # Small delay between commands
                await asyncio.sleep(0.1)
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing unlock step {step.name}: {e}")
            return False
    
    async def _execute_unlock_command(self, command: str, step: UnlockStep, result: UnlockResult) -> bool:
        """Execute individual unlock command"""
        try:
            logger.info(f"Executing unlock command: {command}")
            
            # Check if it's a macro command
            if command in self.macro_engine.list_macros():
                return await self.macro_engine.execute_macro(command)
            
            # Check if it's a UDS command (hex format)
            if all(c in '0123456789ABCDEFabcdef ' for c in command):
                return await self._execute_uds_unlock_command(command)
            
            # Check if it's an ELM327 command
            if command.startswith('at '):
                return await self._execute_elm_unlock_command(command)
            
            # Handle special unlock commands
            if command == "can500":
                await self.macro_engine.execute_macro("can500")
                return True
            elif command.startswith("value"):
                # Handle PyRen value command
                return await self._handle_value_command(command)
            elif command.startswith("_$"):
                # Handle variable expansion
                return await self._handle_variable_command(command)
            else:
                logger.warning(f"Unknown unlock command: {command}")
                return True  # Don't fail on unknown commands
            
        except Exception as e:
            logger.error(f"Error executing unlock command {command}: {e}")
            return False
    
    async def _execute_uds_unlock_command(self, command: str) -> bool:
        """Execute UDS unlock command"""
        try:
            # Convert hex string to bytes
            hex_data = command.replace(' ', '')
            cmd_bytes = bytes.fromhex(hex_data)
            
            # Send UDS command
            from ..obd_interface.uds_transport import UDSMessage
            message = UDSMessage(data=cmd_bytes, target_address=self.uds_session.ecu_address)
            response = await self.uds_session.transport.send_and_receive(message)
            
            if response:
                logger.info(f"UDS unlock response: {response.data.hex()}")
                return True
            else:
                logger.warning(f"No response to UDS unlock command: {command}")
                return False
            
        except Exception as e:
            logger.error(f"Error executing UDS unlock command {command}: {e}")
            return False
    
    async def _execute_elm_unlock_command(self, command: str) -> bool:
        """Execute ELM327 unlock command"""
        try:
            # Remove 'at ' prefix and execute
            elm_cmd = command[3:].strip()
            logger.info(f"ELM unlock command: {elm_cmd}")
            
            # Here you would send the command to the actual ELM327 interface
            # For now, we'll simulate the command execution
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing ELM unlock command {command}: {e}")
            return False
    
    async def _handle_value_command(self, command: str) -> bool:
        """Handle PyRen value command"""
        try:
            # Parse value command: value 1000 7 4 FFFFFFFF
            parts = command.split()
            if len(parts) >= 5:
                value = parts[1]
                param1 = parts[2]
                param2 = parts[3]
                param3 = parts[4]
                
                logger.info(f"Value command: {value} {param1} {param2} {param3}")
                
                # Convert to UDS command and execute
                uds_cmd = f"{value} {param3}"
                return await self._execute_uds_unlock_command(uds_cmd)
            
            return True
            
        except Exception as e:
            logger.error(f"Error handling value command {command}: {e}")
            return False
    
    async def _handle_variable_command(self, command: str) -> bool:
        """Handle variable expansion command"""
        try:
            # Handle _$hexValue type commands
            if command == "_$hexValue":
                # This would typically read a hex value from the interface
                logger.info("Reading hex value from interface")
                return True
            
            return True
            
        except Exception as e:
            logger.error(f"Error handling variable command {command}: {e}")
            return False
    
    async def _handle_user_interaction_step(self, step: UnlockStep, result: UnlockResult) -> bool:
        """Handle step requiring user interaction"""
        try:
            result.status = UnlockStatus.WAITING_USER
            self._notify_progress(result)
            
            self.terminal.print_colored(f"\n{step.description}", TerminalColor.YELLOW)
            
            if step.commands:
                self.terminal.print_colored("Commands to execute:", TerminalColor.CYAN)
                for cmd in step.commands:
                    self.terminal.print_colored(f"  {cmd}", TerminalColor.WHITE)
            
            # Wait for user confirmation
            response = self.terminal.prompt_yes_no("Continue with this step?", default=True)
            
            result.status = UnlockStatus.IN_PROGRESS
            return response
            
        except Exception as e:
            logger.error(f"Error in user interaction step: {e}")
            return False
    
    def _notify_progress(self, result: UnlockResult):
        """Notify progress handlers"""
        for handler in self.progress_handlers:
            try:
                handler(result)
            except Exception as e:
                logger.error(f"Progress handler error: {e}")
    
    def _notify_completion(self, result: UnlockResult):
        """Notify completion handlers"""
        for handler in self.completion_handlers:
            try:
                handler(result)
            except Exception as e:
                logger.error(f"Completion handler error: {e}")
    
    async def stop_unlock(self, unlock_id: str):
        """Stop running unlock procedure"""
        if unlock_id in self.active_unlocks:
            result = self.active_unlocks[unlock_id]
            result.status = UnlockStatus.ABORTED
            result.end_time = datetime.now()
            
            self.terminal.print_status("WARNING", f"Unlock procedure aborted: {result.procedure_name}")
            self._notify_completion(result)
    
    def get_unlock_status(self, unlock_id: str) -> Optional[UnlockResult]:
        """Get unlock procedure status"""
        return self.active_unlocks.get(unlock_id)
    
    def list_procedures(self) -> List[str]:
        """List available unlock procedures"""
        return list(self.procedures.keys())
    
    def get_procedure_info(self, procedure_id: str) -> Optional[Dict[str, Any]]:
        """Get unlock procedure information"""
        if procedure_id not in self.procedures:
            return None
        
        procedure = self.procedures[procedure_id]
        return {
            'name': procedure.name,
            'description': procedure.description,
            'unlock_type': procedure.unlock_type.value,
            'brand': procedure.brand,
            'models': procedure.models,
            'step_count': len(procedure.steps),
            'prerequisites': procedure.prerequisites,
            'warnings': procedure.warnings
        }
