"""
Macro Engine
PyRen-inspired macro execution engine with ANSI terminal support

Features:
- Macro script parsing and execution
- Variable substitution
- Flow control (loops, conditions)
- Interactive prompts
- CAN/K-Line initialization sequences
- Error handling and logging

Based on PyRen macro patterns from references/pyren-pyren3/pyren3/macro/
"""
import asyncio
import logging
import re
import time
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import shlex

from .ansi_terminal import ANSITerminal, TerminalColor
from ..obd_interface.uds_session import UDSSession

logger = logging.getLogger(__name__)


class MacroCommandType(Enum):
    """Macro command types"""
    VARIABLE_SET = "variable_set"
    ELM_COMMAND = "elm_command"
    UDS_COMMAND = "uds_command"
    WAIT = "wait"
    GOTO = "goto"
    LABEL = "label"
    IF_KEY = "if_key"
    PROMPT = "prompt"
    PRINT = "print"
    MACRO_CALL = "macro_call"
    EXIT = "exit"
    COMMENT = "comment"


@dataclass
class MacroVariable:
    """Macro variable definition"""
    name: str
    value: str
    description: Optional[str] = None


@dataclass
class MacroCommand:
    """Macro command structure"""
    line_number: int
    command_type: MacroCommandType
    command: str
    parameters: List[str] = field(default_factory=list)
    raw_line: str = ""


@dataclass
class MacroDefinition:
    """Macro definition"""
    name: str
    description: str
    commands: List[MacroCommand] = field(default_factory=list)
    variables: Dict[str, MacroVariable] = field(default_factory=dict)
    labels: Dict[str, int] = field(default_factory=dict)  # label -> line number


class MacroExecutionContext:
    """Macro execution context"""
    
    def __init__(self):
        self.variables: Dict[str, str] = {}
        self.current_line = 0
        self.call_stack: List[int] = []
        self.is_running = False
        self.should_exit = False
        self.prompt_response: Optional[str] = None


class MacroEngine:
    """
    Macro Engine
    Executes PyRen-style macros with modern async support
    """
    
    def __init__(self, uds_session: Optional[UDSSession] = None, terminal: Optional[ANSITerminal] = None):
        self.uds_session = uds_session
        self.terminal = terminal or ANSITerminal()
        
        # Macro storage
        self.macros: Dict[str, MacroDefinition] = {}
        self.global_variables: Dict[str, str] = {}
        
        # Execution context
        self.context = MacroExecutionContext()
        
        # Event handlers
        self.command_handlers: Dict[str, Callable] = {}
        self.progress_handlers: List[Callable[[str, int, int], None]] = []
        
        self._initialize_default_variables()
        self._initialize_command_handlers()
    
    def _initialize_default_variables(self):
        """Initialize default variables (like PyRen init.txt)"""
        self.global_variables.update({
            'addr': '7A',
            'txa': '7E0',
            'rxa': '7E8',
            'prompt': 'READY'
        })
    
    def _initialize_command_handlers(self):
        """Initialize command handlers"""
        self.command_handlers.update({
            'at': self._handle_elm_command,
            'reset_elm': self._handle_reset_elm,
            'init_can': self._handle_init_can,
            'can250': self._handle_can250,
            'can500': self._handle_can500,
            'init_iso': self._handle_init_iso,
            'slow': self._handle_slow_init,
            'fast': self._handle_fast_init,
            'value': self._handle_value_command,
            'wait': self._handle_wait,
            'goto': self._handle_goto,
            'if_key': self._handle_if_key,
            'exit': self._handle_exit
        })
    
    def parse_macro_file(self, filepath: str) -> MacroDefinition:
        """Parse macro file (PyRen .cmd format)"""
        macro_def = MacroDefinition(
            name=filepath.split('/')[-1].replace('.cmd', ''),
            description=f"Macro from {filepath}"
        )
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                
                if not line or line.startswith('#'):
                    # Comment or empty line
                    command = MacroCommand(
                        line_number=line_num,
                        command_type=MacroCommandType.COMMENT,
                        command=line,
                        raw_line=line
                    )
                    macro_def.commands.append(command)
                    continue
                
                # Parse command
                command = self._parse_command_line(line_num, line)
                macro_def.commands.append(command)
                
                # Handle labels
                if command.command_type == MacroCommandType.LABEL:
                    macro_def.labels[command.command] = line_num
                
                # Handle variable definitions
                if command.command_type == MacroCommandType.VARIABLE_SET:
                    var_name = command.parameters[0] if command.parameters else ""
                    var_value = command.parameters[1] if len(command.parameters) > 1 else ""
                    macro_def.variables[var_name] = MacroVariable(var_name, var_value)
            
            logger.info(f"Parsed macro: {macro_def.name} ({len(macro_def.commands)} commands)")
            return macro_def
            
        except Exception as e:
            logger.error(f"Error parsing macro file {filepath}: {e}")
            raise
    
    def _parse_command_line(self, line_num: int, line: str) -> MacroCommand:
        """Parse individual command line"""
        # Variable assignment: $var = value
        if '=' in line and line.strip().startswith('$'):
            parts = line.split('=', 1)
            var_name = parts[0].strip()[1:]  # Remove $
            var_value = parts[1].strip()
            
            return MacroCommand(
                line_number=line_num,
                command_type=MacroCommandType.VARIABLE_SET,
                command=var_name,
                parameters=[var_name, var_value],
                raw_line=line
            )
        
        # Label: :label_name
        if line.startswith(':'):
            label_name = line[1:].strip()
            return MacroCommand(
                line_number=line_num,
                command_type=MacroCommandType.LABEL,
                command=label_name,
                raw_line=line
            )
        
        # Parse command with parameters
        try:
            parts = shlex.split(line)
        except ValueError:
            # Fallback to simple split if shlex fails
            parts = line.split()
        
        if not parts:
            return MacroCommand(
                line_number=line_num,
                command_type=MacroCommandType.COMMENT,
                command="",
                raw_line=line
            )
        
        command_name = parts[0].lower()
        parameters = parts[1:] if len(parts) > 1 else []
        
        # Determine command type
        if command_name in self.command_handlers:
            if command_name.startswith('at'):
                command_type = MacroCommandType.ELM_COMMAND
            elif command_name in ['wait']:
                command_type = MacroCommandType.WAIT
            elif command_name in ['goto']:
                command_type = MacroCommandType.GOTO
            elif command_name in ['if_key']:
                command_type = MacroCommandType.IF_KEY
            elif command_name in ['exit']:
                command_type = MacroCommandType.EXIT
            else:
                command_type = MacroCommandType.MACRO_CALL
        else:
            # Check if it's a hex command (UDS/OBD)
            if re.match(r'^[0-9A-Fa-f\s]+$', command_name):
                command_type = MacroCommandType.UDS_COMMAND
            else:
                command_type = MacroCommandType.MACRO_CALL
        
        return MacroCommand(
            line_number=line_num,
            command_type=command_type,
            command=command_name,
            parameters=parameters,
            raw_line=line
        )
    
    def register_macro(self, macro_def: MacroDefinition):
        """Register macro definition"""
        self.macros[macro_def.name] = macro_def
        logger.info(f"Registered macro: {macro_def.name}")
    
    def load_macro_directory(self, directory: str):
        """Load all macros from directory"""
        import os
        
        try:
            for filename in os.listdir(directory):
                if filename.endswith('.cmd'):
                    filepath = os.path.join(directory, filename)
                    macro_def = self.parse_macro_file(filepath)
                    self.register_macro(macro_def)
            
            logger.info(f"Loaded {len(self.macros)} macros from {directory}")
            
        except Exception as e:
            logger.error(f"Error loading macro directory {directory}: {e}")
    
    async def execute_macro(self, macro_name: str, variables: Optional[Dict[str, str]] = None) -> bool:
        """Execute macro by name"""
        if macro_name not in self.macros:
            logger.error(f"Macro not found: {macro_name}")
            return False
        
        macro_def = self.macros[macro_name]
        
        # Initialize execution context
        self.context = MacroExecutionContext()
        self.context.variables.update(self.global_variables)
        self.context.variables.update(variables or {})
        self.context.is_running = True
        
        logger.info(f"Executing macro: {macro_name}")
        
        try:
            while self.context.is_running and not self.context.should_exit:
                if self.context.current_line >= len(macro_def.commands):
                    break
                
                command = macro_def.commands[self.context.current_line]
                
                # Notify progress handlers
                for handler in self.progress_handlers:
                    try:
                        handler(macro_name, self.context.current_line, len(macro_def.commands))
                    except Exception as e:
                        logger.error(f"Progress handler error: {e}")
                
                # Execute command
                success = await self._execute_command(command, macro_def)
                
                if not success:
                    logger.error(f"Command failed at line {command.line_number}: {command.raw_line}")
                    return False
                
                # Move to next line (unless command changed current_line)
                if self.context.current_line == command.line_number - 1:
                    self.context.current_line += 1
            
            logger.info(f"Macro execution completed: {macro_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error executing macro {macro_name}: {e}")
            return False
        finally:
            self.context.is_running = False
    
    async def _execute_command(self, command: MacroCommand, macro_def: MacroDefinition) -> bool:
        """Execute individual command"""
        try:
            # Substitute variables in parameters
            substituted_params = []
            for param in command.parameters:
                substituted_params.append(self._substitute_variables(param))
            
            if command.command_type == MacroCommandType.COMMENT:
                return True
            
            elif command.command_type == MacroCommandType.VARIABLE_SET:
                var_name = command.parameters[0]
                var_value = self._substitute_variables(command.parameters[1])
                self.context.variables[var_name] = var_value
                return True
            
            elif command.command_type == MacroCommandType.LABEL:
                return True  # Labels are just markers
            
            elif command.command_type == MacroCommandType.GOTO:
                label = substituted_params[0] if substituted_params else ""
                if label in macro_def.labels:
                    self.context.current_line = macro_def.labels[label] - 1
                else:
                    logger.error(f"Label not found: {label}")
                    return False
                return True
            
            elif command.command_type == MacroCommandType.WAIT:
                wait_time = float(substituted_params[0]) if substituted_params else 1.0
                await asyncio.sleep(wait_time)
                return True
            
            elif command.command_type == MacroCommandType.EXIT:
                self.context.should_exit = True
                return True
            
            elif command.command_type == MacroCommandType.IF_KEY:
                # Simplified if_key implementation
                key = substituted_params[0] if substituted_params else ""
                target = substituted_params[1] if len(substituted_params) > 1 else ""
                
                # Check for key press (simplified)
                if self.context.prompt_response == key:
                    if target in macro_def.labels:
                        self.context.current_line = macro_def.labels[target] - 1
                return True
            
            elif command.command_type == MacroCommandType.MACRO_CALL:
                # Call another macro or command handler
                handler_name = command.command.lower()
                if handler_name in self.command_handlers:
                    handler = self.command_handlers[handler_name]
                    return await handler(substituted_params)
                else:
                    logger.warning(f"Unknown command: {command.command}")
                    return True
            
            elif command.command_type == MacroCommandType.UDS_COMMAND:
                # Execute UDS command
                return await self._execute_uds_command(command.command, substituted_params)
            
            elif command.command_type == MacroCommandType.ELM_COMMAND:
                # Execute ELM327 command
                return await self._execute_elm_command(command.command, substituted_params)
            
            else:
                logger.warning(f"Unhandled command type: {command.command_type}")
                return True
            
        except Exception as e:
            logger.error(f"Error executing command: {e}")
            return False
    
    def _substitute_variables(self, text: str) -> str:
        """Substitute variables in text"""
        result = text
        
        # Replace $variable with value
        for var_name, var_value in self.context.variables.items():
            result = result.replace(f'${var_name}', var_value)
        
        return result
    
    async def _execute_uds_command(self, command: str, parameters: List[str]) -> bool:
        """Execute UDS command"""
        try:
            # Convert hex string to bytes
            hex_data = command.replace(' ', '')
            cmd_bytes = bytes.fromhex(hex_data)
            
            if self.uds_session:
                # Send UDS command
                from ..obd_interface.uds_transport import UDSMessage
                message = UDSMessage(data=cmd_bytes, target_address=int(self.context.variables.get('addr', '7A'), 16))
                response = await self.uds_session.transport.send_and_receive(message)
                
                if response:
                    logger.info(f"UDS response: {response.data.hex()}")
                    return True
                else:
                    logger.warning(f"No response to UDS command: {command}")
                    return False
            else:
                logger.info(f"Simulated UDS command: {command}")
                return True
            
        except Exception as e:
            logger.error(f"Error executing UDS command {command}: {e}")
            return False
    
    async def _execute_elm_command(self, command: str, parameters: List[str]) -> bool:
        """Execute ELM327 command"""
        try:
            full_command = f"{command} {' '.join(parameters)}".strip()
            logger.info(f"ELM command: {full_command}")
            
            # Here you would send the command to the actual ELM327 interface
            # For now, we'll simulate the command execution
            
            return True
            
        except Exception as e:
            logger.error(f"Error executing ELM command {command}: {e}")
            return False
    
    # Command handlers (inspired by PyRen init.txt)
    async def _handle_elm_command(self, parameters: List[str]) -> bool:
        """Handle ELM327 AT commands"""
        command = ' '.join(parameters)
        logger.info(f"ELM AT command: {command}")
        return True
    
    async def _handle_reset_elm(self, parameters: List[str]) -> bool:
        """Handle ELM reset"""
        logger.info("Resetting ELM327")
        self.context.variables['prompt'] = 'ELM'
        return True
    
    async def _handle_init_can(self, parameters: List[str]) -> bool:
        """Handle CAN initialization"""
        logger.info("Initializing CAN")
        # Execute reset_elm macro
        await self._handle_reset_elm([])
        
        # CAN initialization sequence
        can_commands = [
            'at e1', 'at s0', 'at h0', 'at l0', 'at al', 'at caf0', 'at cfc0',
            f"at sh {self.context.variables['txa']}",
            f"at cra {self.context.variables['rxa']}",
            f"at fc sh {self.context.variables['txa']}",
            'at fc sd 30 00 00', 'at fc sm 1'
        ]
        
        for cmd in can_commands:
            await self._execute_elm_command('at', cmd.split()[1:])
        
        self.context.variables['prompt'] = 'CAN'
        return True
    
    async def _handle_can250(self, parameters: List[str]) -> bool:
        """Handle CAN 250kbps initialization"""
        await self._handle_init_can([])
        can250_commands = ['at st ff', 'at at 0', 'at sp 8', 'at at 1']
        
        for cmd in can250_commands:
            await self._execute_elm_command('at', cmd.split()[1:])
        
        self.context.variables['prompt'] = 'CAN250'
        return True
    
    async def _handle_can500(self, parameters: List[str]) -> bool:
        """Handle CAN 500kbps initialization"""
        await self._handle_init_can([])
        can500_commands = ['at st ff', 'at at 0', 'at sp 6', 'at at 1']
        
        for cmd in can500_commands:
            await self._execute_elm_command('at', cmd.split()[1:])
        
        self.context.variables['prompt'] = 'CAN500'
        return True
    
    async def _handle_init_iso(self, parameters: List[str]) -> bool:
        """Handle ISO/K-Line initialization"""
        await self._handle_reset_elm([])
        
        iso_commands = [
            'at e1', 'at l1', 'at d1',
            f"at sh 81 {self.context.variables['addr']} f1",
            'at sw 96',
            f"at wm 81 {self.context.variables['addr']} f1 3E",
            'at ib10', 'at st ff', 'at at 0'
        ]
        
        for cmd in iso_commands:
            await self._execute_elm_command('at', cmd.split()[1:])
        
        self.context.variables['prompt'] = 'KL'
        return True
    
    async def _handle_slow_init(self, parameters: List[str]) -> bool:
        """Handle K-Line slow initialization"""
        await self._handle_init_iso([])
        await self._execute_elm_command('at', ['sp', '4'])
        await self._execute_elm_command('at', ['at', '1'])
        self.context.variables['prompt'] = 'SLOW'
        return True
    
    async def _handle_fast_init(self, parameters: List[str]) -> bool:
        """Handle K-Line fast initialization"""
        await self._handle_init_iso([])
        await self._execute_elm_command('at', ['sp', '5'])
        await self._execute_elm_command('at', ['at', '1'])
        self.context.variables['prompt'] = 'FAST'
        return True
    
    async def _handle_value_command(self, parameters: List[str]) -> bool:
        """Handle value command (PyRen specific)"""
        if len(parameters) >= 4:
            value = parameters[0]
            param1 = parameters[1]
            param2 = parameters[2]
            param3 = parameters[3]
            logger.info(f"Value command: {value} {param1} {param2} {param3}")
        return True
    
    async def _handle_wait(self, parameters: List[str]) -> bool:
        """Handle wait command"""
        wait_time = float(parameters[0]) if parameters else 1.0
        await asyncio.sleep(wait_time)
        return True
    
    async def _handle_goto(self, parameters: List[str]) -> bool:
        """Handle goto command"""
        # This is handled in _execute_command
        return True
    
    async def _handle_if_key(self, parameters: List[str]) -> bool:
        """Handle if_key command"""
        # This is handled in _execute_command
        return True
    
    async def _handle_exit(self, parameters: List[str]) -> bool:
        """Handle exit command"""
        self.context.should_exit = True
        return True
    
    def add_progress_handler(self, handler: Callable[[str, int, int], None]):
        """Add progress handler"""
        self.progress_handlers.append(handler)
    
    def set_variable(self, name: str, value: str):
        """Set global variable"""
        self.global_variables[name] = value
    
    def get_variable(self, name: str) -> Optional[str]:
        """Get variable value"""
        return self.context.variables.get(name) or self.global_variables.get(name)
    
    def list_macros(self) -> List[str]:
        """List available macros"""
        return list(self.macros.keys())
    
    def get_macro_info(self, macro_name: str) -> Optional[Dict[str, Any]]:
        """Get macro information"""
        if macro_name not in self.macros:
            return None
        
        macro_def = self.macros[macro_name]
        return {
            'name': macro_def.name,
            'description': macro_def.description,
            'command_count': len(macro_def.commands),
            'variables': list(macro_def.variables.keys()),
            'labels': list(macro_def.labels.keys())
        }
