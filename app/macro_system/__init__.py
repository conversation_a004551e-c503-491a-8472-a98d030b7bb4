"""
Macro System Module
PyRen-inspired macro system for vehicle-specific configurations and unlock procedures

Features:
- Vehicle model-specific macros
- Unlock procedures (like CLIP device emulation)
- Configuration automation
- Brand-specific initialization sequences
- ANSI terminal control for interactive procedures

Integrates patterns from:
- PyRen macro system: Model-specific configuration macros
- CLIP unlock procedures: Professional diagnostic tool emulation
- ANSI control codes: Terminal-based user interaction
"""

from .macro_engine import MacroEngine, MacroCommand, MacroVariable
from .vehicle_macros import VehicleMacroManager, VehicleModel, MacroCategory
from .unlock_procedures import <PERSON>lockManager, UnlockProcedure, UnlockResult
from .ansi_terminal import ANSITerminal, TerminalColor, TerminalStyle
from .model_configs import ModelConfigManager, VehicleConfig

__all__ = [
    'MacroEngine',
    'MacroCommand',
    'MacroVariable',
    'VehicleMacroManager',
    'VehicleModel',
    'MacroCategory',
    'UnlockManager',
    'UnlockProcedure',
    'UnlockResult',
    'ANSITerminal',
    'TerminalColor',
    'TerminalStyle',
    'ModelConfigManager',
    'VehicleConfig'
]
